# %% [markdown]
# # Optimized Spot Detection Model (2025)
#
# This implementation combines the latest advances in deep learning for microscopy image analysis.
# The model is optimized for both accuracy and speed, with special handling for sparse annotations.

# %%
import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F
from torch.utils.data import Dataset, DataLoader, WeightedRandomSampler
from torchvision import transforms
import tifffile
import matplotlib.pyplot as plt
from sklearn.model_selection import train_test_split
from sklearn.metrics import precision_score, recall_score, f1_score, jaccard_score
import os
import glob
import time
import json
from functools import partial
from typing import List, Tuple, Dict, Optional
import warnings
import traceback
from skimage.transform import resize
import cv2


# Set random seed for reproducibility
torch.manual_seed(42)
np.random.seed(42)

# Suppress some warnings for cleaner output
warnings.filterwarnings('ignore', category=UserWarning)

# Check for GPU availability and configure
device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
print(f"Using device: {device}")
torch.backends.cudnn.benchmark = True  # Enable cuDNN auto-tuner for faster operations

# Enable gradient checkpointing for memory efficiency
torch.utils.checkpoint.checkpoint = lambda f, *args: f(*args)  # Placeholder
GRADIENT_CHECKPOINTING = False

##ON THE FLY
from typing import List, Tuple, Optional
import torch
from torch.utils.data import Dataset
import tifffile
import numpy as np
import cv2
from torchvision import transforms
import traceback
from tqdm import tqdm

class MicroscopyDataset(Dataset):
    """
    Enhanced dataset class for microscopy images with instance segmentation annotations.
    All processing happens on-the-fly as data is requested.
    """
    def __init__(self, image_paths: List[str], mask_paths: List[str],
                 transform: Optional[transforms.Compose] = None,
                 patch_size: int = 256, sparse_threshold: float = 0.01):
        """
        Args:
            image_paths: List of paths to input images
            mask_paths: List of paths to corresponding instance masks
            transform: Optional transforms to apply
            patch_size: Size of patches to extract (default: 256)
            sparse_threshold: Threshold for considering an image sparsely annotated
        """
        self.image_paths = image_paths
        self.mask_paths = mask_paths
        self.transform = transform
        self.patch_size = patch_size
        self.sparse_threshold = sparse_threshold

        # Calculate weights (only needs mask statistics, not full data)
        print("Calculating sampling weights...")
        self.weights = self._calculate_weights()

    def _calculate_weights(self) -> torch.Tensor:
        """Calculate sampling weights based on instance annotation density."""
        weights = []
        for mask_path in tqdm(self.mask_paths, desc="Calculating weights"):
            try:
                # Only load enough of the mask to count instances
                mask = tifffile.imread(mask_path)
                if isinstance(mask, np.memmap):  # For very large files
                    mask = mask[:]  # Load into memory if it's a memmap

                # Count number of unique instances (excluding background)
                num_instances = len(np.unique(mask)) - 1
                # Calculate density as number of instances per patch
                density = num_instances if (mask.shape[0] * mask.shape[1]) == 0 else \
                    num_instances * (self.patch_size**2) / (mask.shape[0] * mask.shape[1])
                weight = 1.0 / (density + self.sparse_threshold)
                weights.append(weight)
            except Exception as e:
                print(f"Warning: Couldn't load {mask_path} for weight calculation: {str(e)}")
                weights.append(1.0)  # Default weight if error occurs

        weights = torch.DoubleTensor(weights)
        if len(weights) > 0:
            weights = weights / torch.sum(weights) * len(weights)
        return weights

    def __len__(self) -> int:
        return len(self.image_paths)
    
    def __getitem__(self, idx: int) -> Tuple[torch.Tensor, torch.Tensor]:
        """Load and preprocess a sample with instance annotations on-the-fly."""
        img_path = self.image_paths[idx]
        mask_path = self.mask_paths[idx]

        # Initialize default empty tensors that will be returned if anything fails
        empty_tensor = torch.zeros((1, self.patch_size, self.patch_size))
        mask_channels = 3  # For semantic, boundary, and heatmap channels
        empty_mask = torch.zeros((mask_channels, self.patch_size, self.patch_size))

        try:
            # Load data on-the-fly only for this sample
            image = tifffile.imread(img_path)
            mask = tifffile.imread(mask_path)

            # Normalize image
            if np.issubdtype(image.dtype, np.floating):
                image = image.astype(np.float32)
            else:
                image = image.astype(np.float32)
                if np.max(image) > 0:
                    image = image / np.max(image)
            # Clip image values to [0, 1] to avoid outliers
            image = np.clip(image, 0.0, 1.0)

            # Check for NaN/Inf in full image before cropping
            if np.isnan(image).any() or np.isinf(image).any():
                print(f"Warning: NaN/Inf detected in full image at idx {idx}")
                image = np.nan_to_num(image, nan=0.0, posinf=1.0, neginf=0.0)

            # Determine patch location
            x_start = np.random.randint(0, max(1, image.shape[0] - self.patch_size + 1))
            y_start = np.random.randint(0, max(1, image.shape[1] - self.patch_size + 1))

            # Try to find a better location near instances if available
            unique_instances = np.unique(mask)
            if len(unique_instances) > 1:
                non_zero_instances = unique_instances[unique_instances > 0]
                if len(non_zero_instances) > 0:
                    target_instance = np.random.choice(non_zero_instances)
                    instance_mask = (mask == target_instance).astype(np.uint8)
                    instance_coords = np.argwhere(instance_mask)
                    if len(instance_coords) > 0:
                        x_center, y_center = np.mean(instance_coords, axis=0).astype(int)
                        x_start = max(0, x_center - self.patch_size//2)
                        y_start = max(0, y_center - self.patch_size//2)

            # Extract patch (on-the-fly processing)
            image_patch = image[x_start:x_start+self.patch_size, y_start:y_start+self.patch_size]
            mask_patch = mask[x_start:x_start+self.patch_size, y_start:y_start+self.patch_size]

            # Check for NaN/Inf in patch before padding
            if np.isnan(image_patch).any() or np.isinf(image_patch).any():
                print(f"Warning: NaN/Inf detected in image patch idx {idx}")
                image_patch = np.nan_to_num(image_patch, nan=0.0, posinf=1.0, neginf=0.0)
            if np.isnan(mask_patch).any() or np.isinf(mask_patch).any():
                print(f"Warning: NaN/Inf detected in mask patch idx {idx}")
                mask_patch = np.nan_to_num(mask_patch, nan=0.0, posinf=0.0, neginf=0.0)

            # Handle edge cases with padding
            if image_patch.shape[0] != self.patch_size or image_patch.shape[1] != self.patch_size:
                pad_x = self.patch_size - image_patch.shape[0]
                pad_y = self.patch_size - image_patch.shape[1]
                if pad_x > 0 or pad_y > 0:
                    image_patch = np.pad(image_patch, ((0, pad_x), (0, pad_y)), mode='reflect')
                    mask_patch = np.pad(mask_patch, ((0, pad_x), (0, pad_y)), mode='constant', constant_values=0)

            # Create mask outputs
            semantic_mask = (mask_patch > 0).astype(np.float32)
            boundary_mask = np.zeros_like(mask_patch, dtype=np.float32)
            instance_ids = np.unique(mask_patch)

            for instance_id in instance_ids:
                if instance_id == 0:  # Skip background
                    continue
                instance_mask = (mask_patch == instance_id).astype(np.uint8)
                kernel = np.ones((3,3), np.uint8)
                try:
                    eroded = cv2.erode(instance_mask, kernel, iterations=1)
                    boundary = instance_mask - eroded
                    boundary_mask[boundary > 0] = 1
                except Exception as e:
                    continue

            # Create heatmap
            heatmap = np.zeros_like(mask_patch, dtype=np.float32)
            for instance_id in instance_ids:
                if instance_id == 0:  # Skip background
                    continue
                instance_mask = (mask_patch == instance_id).astype(np.uint8)
                coords = np.argwhere(instance_mask)
                if len(coords) > 0:
                    centroid = np.mean(coords, axis=0).astype(int)
                    height, width = heatmap.shape
                    x, y = centroid
                    sigma = 5
                    x_grid, y_grid = np.meshgrid(np.arange(width), np.arange(height))
                    gaussian = np.exp(-((x_grid - y)**2 + (y_grid - x)**2) / (2 * sigma**2))
                    heatmap += gaussian
            heatmap = np.clip(heatmap, 0, 1)

            # Apply transforms if specified
            if self.transform:
                try:
                    image_patch_pil = transforms.ToPILImage()(image_patch)
                    seed = np.random.randint(2147483647)
                    torch.manual_seed(seed)
                    image_patch = self.transform(image_patch_pil)

                    # Convert transformed image to numpy to get dimensions
                    if isinstance(image_patch, torch.Tensor):
                        image_patch_np = image_patch.squeeze().numpy()
                    else:
                        image_patch_np = np.array(image_patch)

                    from skimage.transform import resize
                    new_size = (image_patch_np.shape[0], image_patch_np.shape[1])

                    semantic_mask = resize(semantic_mask, new_size, order=0, preserve_range=True, anti_aliasing=False)
                    boundary_mask = resize(boundary_mask, new_size, order=0, preserve_range=True, anti_aliasing=False)
                    heatmap = resize(heatmap, new_size, order=1, preserve_range=True)

                    # Threshold masks after resize to keep binary
                    semantic_mask = (semantic_mask > 0.5).astype(np.float32)
                    boundary_mask = (boundary_mask > 0.5).astype(np.float32)
                    heatmap = np.clip(heatmap, 0, 1)

                    if not isinstance(image_patch, torch.Tensor):
                        image_tensor = transforms.ToTensor()(image_patch)
                    else:
                        image_tensor = image_patch.unsqueeze(0) if image_patch.ndim == 2 else image_patch
                except Exception as e:
                    print(f"Transform error at idx {idx}: {str(e)}")
                    image_tensor = torch.FloatTensor(image_patch).unsqueeze(0)

                # Convert masks to tensors
                semantic_tensor = torch.FloatTensor(semantic_mask).unsqueeze(0)
                boundary_tensor = torch.FloatTensor(boundary_mask).unsqueeze(0)
                heatmap_tensor = torch.FloatTensor(heatmap).unsqueeze(0)
                mask_tensor = torch.cat([semantic_tensor, boundary_tensor, heatmap_tensor], dim=0)
            else:
                image_tensor = torch.FloatTensor(image_patch).unsqueeze(0)
                semantic_tensor = torch.FloatTensor(semantic_mask).unsqueeze(0)
                boundary_tensor = torch.FloatTensor(boundary_mask).unsqueeze(0)  # fixed variable name
                heatmap_tensor = torch.FloatTensor(heatmap).unsqueeze(0)
                mask_tensor = torch.cat([semantic_tensor, boundary_tensor, heatmap_tensor], dim=0)

            # Warn if semantic mask is empty (may cause issues in loss)
            if semantic_tensor.sum() == 0:
                print(f"Warning: Empty semantic mask at idx {idx}")

            return image_tensor, mask_tensor

        except Exception as e:
            print(f"Error processing sample {idx}: {str(e)}")
            return empty_tensor, empty_mask


# ## FOR DEBUGGING USE THIS 
# class MicroscopyDataset(Dataset):
#     """
#     Enhanced dataset class for microscopy images with instance segmentation annotations.
#     Handles patch extraction, data augmentation, and instance-aware sampling.
#     """
#     def __init__(self, image_paths: List[str], mask_paths: List[str],
#                  transform: Optional[transforms.Compose] = None,
#                  patch_size: int = 256, sparse_threshold: float = 0.01):
#         """
#         Args:
#             image_paths: List of paths to input images
#             mask_paths: List of paths to corresponding instance masks
#             transform: Optional transforms to apply
#             patch_size: Size of patches to extract (default: 256)
#             sparse_threshold: Threshold for considering an image sparsely annotated
#         """
#         self.image_paths = image_paths
#         self.mask_paths = mask_paths
#         self.transform = transform
#         self.patch_size = patch_size
#         self.sparse_threshold = sparse_threshold
#         self.weights = self._calculate_weights()

#     def _calculate_weights(self) -> torch.Tensor:
#         """Calculate sampling weights based on instance annotation density."""
#         weights = []
#         for mask_path in self.mask_paths:
#             try:
#                 mask = tifffile.imread(mask_path)
#                 # Count number of unique instances (excluding background)
#                 num_instances = len(np.unique(mask)) - 1
#                 # Calculate density as number of instances per patch
#                 density = num_instances * (self.patch_size**2) / (mask.shape[0] * mask.shape[1])
#                 weight = 1.0 / (density + self.sparse_threshold)
#                 weights.append(weight)
#             except:
#                 weights.append(1.0)  # Default weight if error occurs
#         weights = torch.DoubleTensor(weights)
#         weights = weights / torch.sum(weights) * len(weights)
#         return weights

#     def __len__(self) -> int:
#         return len(self.image_paths)

#     def __getitem__(self, idx: int) -> Tuple[torch.Tensor, torch.Tensor]:
#         """Load and preprocess a sample with instance annotations."""
#         img_path = self.image_paths[idx]
#         mask_path = self.mask_paths[idx]
#         # print(f"\nProcessing index {idx}:")
#         # print(f"Image path: {img_path}")
#         # print(f"Mask path: {mask_path}")

#         # Initialize default empty tensors that will be returned if anything fails
#         empty_tensor = torch.zeros((1, self.patch_size, self.patch_size))
#         mask_channels = 3  # For semantic, boundary, and heatmap channels
#         empty_mask = torch.zeros((mask_channels, self.patch_size, self.patch_size))

#         try:
#             # 1. Load image and mask
#            #print("Loading images...")
#             try:
#                 image = tifffile.imread(img_path)
#                 mask = tifffile.imread(mask_path)
#                #print(f"Image shape: {image.shape}, dtype: {image.dtype}")
#                #print(f"Mask shape: {mask.shape}, dtype: {mask.dtype}")
#                #print(f"Unique mask values: {np.unique(mask)}")
#             except Exception as e:
#                 print(f"Error loading images: {str(e)}")
#                 return empty_tensor, empty_mask

#             # 2. Normalize image
#            #print("Normalizing image...")
#             try:
#                 if np.issubdtype(image.dtype, np.floating):
#                     image = image.astype(np.float32)
#                 else:
#                     # Convert to float and normalize
#                     image = image.astype(np.float32)
#                     if np.max(image) > 0:
#                         image = image / np.max(image)
#                #print(f"After normalization - min: {np.min(image)}, max: {np.max(image)}")
#             except Exception as e:
#                 print(f"Error normalizing image: {str(e)}")
#                 return empty_tensor, empty_mask

#             # 3. Determine patch location
#            #print("Selecting patch location...")
#             try:
#                 # Default to random position
#                 x_start = np.random.randint(0, max(1, image.shape[0] - self.patch_size + 1))
#                 y_start = np.random.randint(0, max(1, image.shape[1] - self.patch_size + 1))

#                 # Try to find a better location near instances if available
#                 unique_instances = np.unique(mask)
#                 print(f"Found {len(unique_instances)-1} instances in this mask")

#                 if len(unique_instances) > 1:  # If there are instances (background is usually 0)
#                     non_zero_instances = unique_instances[unique_instances > 0]
#                     if len(non_zero_instances) > 0:
#                         # Randomly select an instance to center on
#                         target_instance = np.random.choice(non_zero_instances)
#                         instance_mask = (mask == target_instance).astype(np.uint8)
#                         print(f"Selected instance {target_instance} with area {np.sum(instance_mask)}")

#                         # Find center of this instance
#                         instance_coords = np.argwhere(instance_mask)
#                         if len(instance_coords) > 0:
#                             x_center, y_center = np.mean(instance_coords, axis=0).astype(int)
#                             x_start = max(0, x_center - self.patch_size//2)
#                             y_start = max(0, y_center - self.patch_size//2)
#                             print(f"Centered patch on instance at ({x_center}, {y_center})")
#                         else:
#                             print("Selected instance has no coordinates - using random position")
#                     else:
#                         print("No instances found - using random position")
#                 else:
#                     print("No instances in mask - using random position")
#             except Exception as e:
#                 print(f"Error determining patch location: {str(e)}")
#                 # Fall back to center of image if anything goes wrong
#                 x_start = (image.shape[0] - self.patch_size) // 2
#                 y_start = (image.shape[1] - self.patch_size) // 2

#             print(f"Selected patch position: ({x_start}, {y_start})")

#             # 4. Extract patch
#            #print("Extracting patch...")
#             try:
#                 image_patch = image[x_start:x_start+self.patch_size, y_start:y_start+self.patch_size]
#                 mask_patch = mask[x_start:x_start+self.patch_size, y_start:y_start+self.patch_size]

#                 # Handle edge cases with padding
#                 if image_patch.shape[0] != self.patch_size or image_patch.shape[1] != self.patch_size:
#                     print(f"Need padding - original patch shape: {image_patch.shape}")
#                     pad_x = self.patch_size - image_patch.shape[0]
#                     pad_y = self.patch_size - image_patch.shape[1]
#                     if pad_x > 0 or pad_y > 0:
#                         image_patch = np.pad(image_patch, ((0, pad_x), (0, pad_y)), mode='reflect')
#                         mask_patch = np.pad(mask_patch, ((0, pad_x), (0, pad_y)), mode='constant', constant_values=0)
#                         print(f"After padding: image={image_patch.shape}, mask={mask_patch.shape}")

#                #print(f"Patch shapes - image: {image_patch.shape}, mask: {mask_patch.shape}")
#             except Exception as e:
#                 print(f"Error extracting patch: {str(e)}")
#                 return empty_tensor, empty_mask

#             # 5. Create mask outputs
#            #print("Creating mask outputs...")
#             try:
#                 # Semantic mask (foreground vs background)
#                 semantic_mask = (mask_patch > 0).astype(np.float32)

#                 # Boundary mask
#                 boundary_mask = np.zeros_like(mask_patch, dtype=np.float32)
#                 instance_ids = np.unique(mask_patch)
#                 for instance_id in instance_ids:
#                     if instance_id == 0:  # Skip background
#                         continue
#                     instance_mask = (mask_patch == instance_id).astype(np.uint8)
#                     # Create boundary by morphological operations
#                     kernel = np.ones((3,3), np.uint8)
#                     try:
#                         eroded = cv2.erode(instance_mask, kernel, iterations=1)
#                         boundary = instance_mask - eroded
#                         boundary_mask[boundary > 0] = 1
#                     except Exception as e:
#                         print(f"Error creating boundary for instance {instance_id}: {str(e)}")
#                         continue

#                 # Centroid heatmap
#                 heatmap = np.zeros_like(mask_patch, dtype=np.float32)
#                 for instance_id in instance_ids:
#                     if instance_id == 0:  # Skip background
#                         continue
#                     instance_mask = (mask_patch == instance_id).astype(np.uint8)
#                     # Find centroid
#                     coords = np.argwhere(instance_mask)
#                     if len(coords) > 0:
#                         centroid = np.mean(coords, axis=0).astype(int)
#                         # Create Gaussian around centroid
#                         height, width = heatmap.shape
#                         x, y = centroid
#                         sigma = 5  # Adjust based on your expected spot size
#                         x_grid, y_grid = np.meshgrid(np.arange(width), np.arange(height))
#                         gaussian = np.exp(-((x_grid - y)**2 + (y_grid - x)**2) / (2 * sigma**2))
#                         heatmap += gaussian
#                 heatmap = np.clip(heatmap, 0, 1)
#             except Exception as e:
#                 print(f"Error creating mask outputs: {str(e)}")
#                 # Fall back to empty masks
#                 semantic_mask = np.zeros((self.patch_size, self.patch_size), dtype=np.float32)
#                 boundary_mask = np.zeros((self.patch_size, self.patch_size), dtype=np.float32)
#                 heatmap = np.zeros((self.patch_size, self.patch_size), dtype=np.float32)

#             # 6. Apply transforms if specified
#             if self.transform:
#                #print("Applying transforms...")
#                 try:
#                     # Convert to PIL Image for transforms
#                     image_patch_pil = transforms.ToPILImage()(image_patch)
#                     seed = np.random.randint(2147483647)
#                     torch.manual_seed(seed)
#                     image_patch = self.transform(image_patch_pil)

#                     # For masks, we'll use the same transforms but need to handle them as numpy arrays
#                     # Convert the transformed image back to numpy to get dimensions
#                     if isinstance(image_patch, torch.Tensor):
#                         image_patch_np = image_patch.squeeze().numpy()
#                     else:
#                         image_patch_np = np.array(image_patch)

#                     # Resize masks to match transformed image dimensions
#                     from skimage.transform import resize
#                     new_size = (image_patch_np.shape[0], image_patch_np.shape[1])

#                     semantic_mask = resize(semantic_mask, new_size, preserve_range=True)
#                     boundary_mask = resize(boundary_mask, new_size, preserve_range=True)
#                     heatmap = resize(heatmap, new_size, preserve_range=True)

#                     # Convert to tensors
#                     semantic_tensor = torch.FloatTensor(semantic_mask).unsqueeze(0)
#                     boundary_tensor = torch.FloatTensor(boundary_mask).unsqueeze(0)
#                     heatmap_tensor = torch.FloatTensor(heatmap).unsqueeze(0)
#                     mask_tensor = torch.cat([semantic_tensor, boundary_tensor, heatmap_tensor], dim=0)

#                     # Convert image to tensor if it's not already
#                     if not isinstance(image_patch, torch.Tensor):
#                         image_tensor = transforms.ToTensor()(image_patch)
#                     else:
#                         image_tensor = image_patch.unsqueeze(0) if image_patch.ndim == 2 else image_patch

#                 except Exception as e:
#                     print(f"Transform failed: {str(e)}")
#                     # Fall back to untransformed data
#                     image_tensor = torch.FloatTensor(image_patch).unsqueeze(0)
#                     semantic_tensor = torch.FloatTensor(semantic_mask).unsqueeze(0)
#                     boundary_tensor = torch.FloatTensor(boundaryMask).unsqueeze(0)
#                     heatmap_tensor = torch.FloatTensor(heatmap).unsqueeze(0)
#                     mask_tensor = torch.cat([semantic_tensor, boundary_tensor, heatmap_tensor], dim=0)
#             else:
#                 # No transforms - convert directly to tensors
#                #print("No transforms - converting to tensors...")
#                 image_tensor = torch.FloatTensor(image_patch).unsqueeze(0)
#                 semantic_tensor = torch.FloatTensor(semantic_mask).unsqueeze(0)
#                 boundary_tensor = torch.FloatTensor(boundaryMask).unsqueeze(0)
#                 heatmap_tensor = torch.FloatTensor(heatmap).unsqueeze(0)
#                 mask_tensor = torch.cat([semantic_tensor, boundary_tensor, heatmap_tensor], dim=0)

#         except Exception as e:
#             print(f"Unexpected error processing sample {idx}: {str(e)}")
#             print(traceback.format_exc())  # Print full stack trace for debugging
#             return empty_tensor, empty_mask

#         # Verify tensor shapes before returning
#         # print(f"Final tensor shapes - image: {image_tensor.shape}, mask: {mask_tensor.shape}")
#         # print(f"Mask tensor range - semantic: [{mask_tensor[0].min().item()}, {mask_tensor[0].max().item()}]")
#         # print(f"Mask tensor range - boundary: [{mask_tensor[1].min().item()}, {mask_tensor[1].max().item()}]")
#         # print(f"Mask tensor range - heatmap: [{mask_tensor[2].min().item()}, {mask_tensor[2].max().item()}]")

#         return image_tensor, mask_tensor


import torch
import torch.nn as nn
import torch.nn.functional as F
from torch.nn.functional import scaled_dot_product_attention

class SwinV3Block(nn.Module):
    """
    Swin Transformer V3 block with shifted windows, relative position bias,
    LayerScale normalization, and efficient scaled-dot-product attention.
    """
    def __init__(self, dim, num_heads, window_size=8, shift_size=0):
        super().__init__()
        assert 0 <= shift_size < window_size
        self.dim = dim
        self.num_heads = num_heads
        self.window_size = window_size
        self.shift_size = shift_size
        self.head_dim = dim // num_heads

        # normalization & mlp
        self.norm1 = nn.GroupNorm(1, dim)
        self.norm2 = nn.GroupNorm(1, dim)
        self.mlp = nn.Sequential(
            nn.Linear(dim, 4 * dim),
            nn.GELU(),
            nn.Linear(4 * dim, dim),
        )

        # LayerScale parameters
        self.gamma1 = nn.Parameter(torch.ones(dim))
        self.gamma2 = nn.Parameter(torch.ones(dim))

        # relative position bias table
        table_size = (2*window_size - 1)**2
        self.relative_position_bias_table = nn.Parameter(torch.zeros(table_size, num_heads))
        self.register_buffer("relative_position_index", self._make_rel_pos_index(window_size))

        # q/k/v projections
        self.qkv = nn.Linear(dim, dim * 3, bias=False)
        self.proj = nn.Linear(dim, dim)

    def _make_rel_pos_index(self, ws):
        coords = torch.stack(torch.meshgrid(torch.arange(ws), torch.arange(ws), indexing="ij"))  # 2, ws, ws
        coords = coords.flatten(1)  # 2, ws*ws
        rel = coords[:, :, None] - coords[:, None, :]  # 2, N, N
        rel = rel.permute(1,2,0).contiguous()  # N, N, 2
        rel[...,0] += ws - 1
        rel[...,1] += ws - 1
        rel[...,0] *= 2*ws - 1
        index = rel.sum(-1)
        return index.long()  # (N, N)

    def _get_shift_mask(self, H, W, device):
        """Build per-window mask of shape (num_windows, ws*ws, ws*ws)"""
        if self.shift_size == 0:
            return None
        ws, s = self.window_size, self.shift_size
        img_mask = torch.zeros((1, H, W, 1), device=device, dtype=torch.long)
        h_slices = (slice(0, -ws), slice(-ws, -s), slice(-s, None))
        w_slices = (slice(0, -ws), slice(-ws, -s), slice(-s, None))
        cnt = 0
        for hs in h_slices:
            for ws_ in w_slices:
                img_mask[:, hs, ws_, :] = cnt
                cnt += 1

        # partition
        B = 1
        mask_windows = img_mask.view(
            B, H//ws, ws, W//ws, ws, 1
        ).permute(0,1,3,2,4,5).reshape(-1, ws*ws)
        # compute mask diff
        diff = mask_windows.unsqueeze(1) - mask_windows.unsqueeze(2)
        diff = diff.to(torch.float32)  # <-- FIX: cast to float before masked_fill
        mask = diff.masked_fill(diff!=0, float('-inf'))
        return mask  # (num_windows, ws*ws, ws*ws)

    def forward(self, x):
        B, C, H0, W0 = x.shape
        ws, s = self.window_size, self.shift_size

        # pad if not divisible
        pad_h = (ws - H0 % ws) % ws
        pad_w = (ws - W0 % ws) % ws
        if pad_h or pad_w:
            x = F.pad(x, (0,pad_w,0,pad_h))
        H, W = x.shape[2:]

        # Debug: check input
        if not torch.isfinite(x).all():
            print('[DEBUG][SwinV3Block] NaN/Inf after pad')

        # 1. Norm + (optional) shift
        x_norm = self.norm1(x)
        if not torch.isfinite(x_norm).all():
            print('[DEBUG][SwinV3Block] NaN/Inf after norm1')
        if s > 0:
            x_norm = torch.roll(x_norm, shifts=(-s, -s), dims=(2,3))
        if not torch.isfinite(x_norm).all():
            print('[DEBUG][SwinV3Block] NaN/Inf after shift')

        # 2. Window partition
        x_win = x_norm.view(B, C, H//ws, ws, W//ws, ws)
        x_win = x_win.permute(0,2,4,3,5,1).contiguous()
        x_win = x_win.view(-1, ws*ws, C)
        if not torch.isfinite(x_win).all():
            print('[DEBUG][SwinV3Block] NaN/Inf after window partition')

        # 3. qkv and reshape to heads
        qkv = self.qkv(x_win)
        q,k,v = qkv.chunk(3, dim=-1)
        q = q.view(-1, ws*ws, self.num_heads, self.head_dim).transpose(1,2)
        k = k.view(-1, ws*ws, self.num_heads, self.head_dim).transpose(1,2)
        v = v.view(-1, ws*ws, self.num_heads, self.head_dim).transpose(1,2)
        if not torch.isfinite(q).all() or not torch.isfinite(k).all() or not torch.isfinite(v).all():
            print('[DEBUG][SwinV3Block] NaN/Inf in q/k/v')

        # 4. relative position bias
        N = ws*ws
        bias = self.relative_position_bias_table[self.relative_position_index.view(-1)]
        bias = bias.view(N, N, -1).permute(2,0,1)
        bias = bias.unsqueeze(0).expand(q.shape[0], -1, -1, -1)
        if not torch.isfinite(bias).all():
            print('[DEBUG][SwinV3Block] NaN/Inf in relative position bias')

        # 5. shifted-window mask
        mask = self._get_shift_mask(H, W, x.device)
        N = ws*ws
        num_windows = (H // ws) * (W // ws)
        if mask is not None:
            # mask: (num_windows, N, N)
            mask = mask.unsqueeze(0).repeat(B, 1, 1, 1)  # (B, num_windows, N, N)
            mask = mask.reshape(B * num_windows, N, N)   # (B*num_windows, N, N)
            mask = mask.unsqueeze(1).expand(-1, self.num_heads, N, N)  # (B*num_windows, heads, N, N)
            # bias: (B*num_windows, heads, N, N)
            attn_mask = mask + bias
        else:
            attn_mask = bias
        if not torch.isfinite(attn_mask).all():
            print('[DEBUG][SwinV3Block] NaN/Inf in attn_mask')
        attn_out = scaled_dot_product_attention(
            q, k, v,
            attn_mask = attn_mask,
            dropout_p = 0.0,
            is_causal = False
        )
        if not torch.isfinite(attn_out).all():
            print('[DEBUG][SwinV3Block] NaN/Inf after attention')
            attn_out = torch.nan_to_num(attn_out, nan=0.0, posinf=1.0, neginf=-1.0)

        # 6. merge heads & windows back
        attn_out = attn_out.transpose(1,2).reshape(-1, N, C)
        x_win = self.proj(attn_out)
        if not torch.isfinite(x_win).all():
            print('[DEBUG][SwinV3Block] NaN/Inf after proj')

        # reverse windows
        x_win = x_win.view(B, H//ws, W//ws, ws, ws, C)
        x_win = x_win.permute(0,5,1,3,2,4).contiguous()
        x_attn = x_win.view(B, C, H, W)
        if not torch.isfinite(x_attn).all():
            print('[DEBUG][SwinV3Block] NaN/Inf after reverse windows')

        # undo shift
        if s>0:
            x_attn = torch.roll(x_attn, shifts=(s,s), dims=(2,3))
        if not torch.isfinite(x_attn).all():
            print('[DEBUG][SwinV3Block] NaN/Inf after undo shift')

        # residual + LayerScale
        x = x + self.gamma1.view(1,-1,1,1) * x_attn
        if not torch.isfinite(x).all():
            print('[DEBUG][SwinV3Block] NaN/Inf after residual+LayerScale')

        # MLP
        y = self.norm2(x)
        if not torch.isfinite(y).all():
            print('[DEBUG][SwinV3Block] NaN/Inf after norm2')
        y = y.flatten(2).transpose(1,2)
        y = self.mlp(y)
        y = y.transpose(1,2).view(B,C,H,W)
        if not torch.isfinite(y).all():
            print('[DEBUG][SwinV3Block] NaN/Inf after mlp')
        x = x + self.gamma2.view(1,-1,1,1) * y
        if not torch.isfinite(x).all():
            print('[DEBUG][SwinV3Block] NaN/Inf after final residual')

        # un‐pad if needed
        if pad_h or pad_w:
            x = x[..., :H0, :W0]
        if not torch.isfinite(x).all():
            print('[DEBUG][SwinV3Block] NaN/Inf after unpad')

        return x


    def _mlp_forward(self, x):
        if GRADIENT_CHECKPOINTING:
            return torch.utils.checkpoint.checkpoint(
                self.mlp, x.flatten(2).transpose(1,2)
            ).transpose(1,2).view(x.shape)
        return self.mlp(x.flatten(2).transpose(1,2)).transpose(1,2).view(x.shape)


class DynamicConv2d(nn.Module):
    """
    Optimized Dynamic Convolution with:
    - Memory-efficient kernel generation
    - Adaptive receptive fields
    - Gradient checkpointing support
    """
    def __init__(self, in_channels, out_channels, kernel_size=3):
        super().__init__()
        self.kernel_size = kernel_size
        self.in_channels = in_channels
        self.out_channels = out_channels

        self.kernel_gen = nn.Sequential(
            nn.AdaptiveAvgPool2d(1),
            nn.Conv2d(in_channels, out_channels * kernel_size * kernel_size, kernel_size=1),
            nn.Unflatten(1, (out_channels, kernel_size * kernel_size))
        )
        self.reset_parameters()

    def reset_parameters(self):
        for m in self.modules():
            if isinstance(m, nn.Conv2d):
                nn.init.kaiming_normal_(m.weight, mode='fan_out', nonlinearity='relu')

    def _get_default_kernel(self, device):
        return torch.randn(
            self.out_channels, self.in_channels, self.kernel_size, self.kernel_size, device=device
        ) * 0.01

    def forward(self, x):
        B, C, H, W = x.shape
        try:
            kernels = self.kernel_gen(x)
            kernels = kernels.reshape(B * self.out_channels, 1, self.kernel_size, self.kernel_size)

            input_groups = x.reshape(1, B * C, H, W)
            if kernels.shape[1] == 1 and C != 1:
                kernels = kernels.repeat(1, C, 1, 1)

            output = F.conv2d(
                input_groups,
                kernels,
                padding=self.kernel_size // 2,
                groups=B
            )
            return output.reshape(B, self.out_channels, H, W)
        except Exception as e:
            print(f"[DynamicConv2d] Error: {e}. Falling back to default kernel.")
            fallback_kernel = self._get_default_kernel(x.device)
            return F.conv2d(x, fallback_kernel, padding=self.kernel_size // 2)


class CrossScaleAttention(nn.Module):
    """
    Optimized Cross-scale attention module with:
    - Memory-efficient implementation
    - Gradient checkpointing support
    """
    def __init__(self, channels):
        super().__init__()
        self.query = nn.Conv2d(channels, channels, kernel_size=1)
        self.key = nn.Conv2d(channels, channels, kernel_size=1)
        self.value = nn.Conv2d(channels, channels, kernel_size=1)
        self.gamma = nn.Parameter(torch.zeros(1))
        self.softmax = nn.Softmax(dim=-1)

    def forward(self, x, y):
        B, C, H, W = x.shape

        query = self.query(x).view(B, C, -1).permute(0, 2, 1)   # B, HW, C
        key = self.key(y).view(B, C, -1)                        # B, C, H'W'
        value = self.value(y).view(B, C, -1).permute(0, 2, 1)   # B, H'W', C

        attn = torch.bmm(query, key)                            # B, HW, H'W'
        attn = self.softmax(attn)
        out = torch.bmm(attn, value).permute(0, 2, 1).view(B, C, H, W)

        return self.gamma * out + x

class SpotDetector2025(nn.Module):
    """
    Optimized state-of-the-art spot detection model for 2025.
    Combines Swin Transformer V3 blocks, dynamic convolutions, and cross-scale attention.
    """
    def __init__(self, input_channels=1, num_classes=1):
        super().__init__()

        self.stem = nn.Sequential(
            nn.Conv2d(input_channels, 64, kernel_size=3, padding=1),
            nn.BatchNorm2d(64),
            nn.SiLU()
        )

        self.encoder1 = self._make_encoder_block(64, 128)
        self.encoder2 = self._make_encoder_block(128, 256)
        self.encoder3 = self._make_encoder_block(256, 512)

        self.bottleneck = nn.Sequential(
            SwinV3Block(512, 8),
            SwinV3Block(512, 8, shift_size=4),
            DynamicConv2d(512, 512)
        )

        self.decoder3 = self._make_decoder_block(512, 256)
        self.decoder2 = self._make_decoder_block(256, 128)
        self.decoder1 = self._make_decoder_block(128, 64)

        self.cross_attn1 = CrossScaleAttention(256)
        self.cross_attn2 = CrossScaleAttention(128)

        self.heatmap_head = nn.Sequential(
            nn.Conv2d(64, 64, kernel_size=3, padding=1),
            nn.BatchNorm2d(64),
            nn.SiLU(),
            nn.Conv2d(64, num_classes, kernel_size=1)
        )

        self.flow_head = nn.Sequential(
            nn.Conv2d(64, 64, kernel_size=3, padding=1),
            nn.BatchNorm2d(64),
            nn.SiLU(),
            nn.Conv2d(64, 2, kernel_size=1)
        )

        self.scale_head = nn.Sequential(
            nn.Conv2d(64, 64, kernel_size=3, padding=1),
            nn.BatchNorm2d(64),
            nn.SiLU(),
            nn.Conv2d(64, 1, kernel_size=1),
            nn.Sigmoid()
        )

        self.apply(self._init_weights)

    def _make_encoder_block(self, in_channels, out_channels):
        return nn.Sequential(
            nn.Conv2d(in_channels, out_channels, kernel_size=3, padding=1),
            nn.BatchNorm2d(out_channels),
            nn.SiLU(),
            SwinV3Block(out_channels, out_channels // 32),
            DynamicConv2d(out_channels, out_channels),
            nn.BatchNorm2d(out_channels),
            nn.SiLU(),
            nn.MaxPool2d(2)
        )

    def _make_decoder_block(self, in_channels, out_channels):
        return nn.Sequential(
            nn.ConvTranspose2d(in_channels, out_channels, kernel_size=2, stride=2),
            nn.BatchNorm2d(out_channels),
            nn.SiLU(),
            SwinV3Block(out_channels, out_channels // 32),
            DynamicConv2d(out_channels, out_channels),
            nn.BatchNorm2d(out_channels),
            nn.SiLU()
        )

    def _init_weights(self, m):
        if isinstance(m, nn.Linear):
            nn.init.xavier_normal_(m.weight)
            if m.bias is not None:
                nn.init.constant_(m.bias, 0)
        elif isinstance(m, nn.Conv2d):
            nn.init.kaiming_normal_(m.weight, mode='fan_out', nonlinearity='relu')
            if m.bias is not None:
                nn.init.constant_(m.bias, 0)
        elif isinstance(m, nn.LayerNorm):
            nn.init.constant_(m.weight, 1)
            nn.init.constant_(m.bias, 0)


    def forward(self, x):
        if not torch.is_tensor(x) or x.dim() != 4:
            raise ValueError(f"Expected 4D tensor input, got {type(x)} with shape {x.shape if isinstance(x, torch.Tensor) else 'N/A'}")

        # Debug: check input for NaN/Inf
        if not torch.isfinite(x).all():
            print("[DEBUG] NaN or Inf detected in model input!")
            print(f"Input stats: min={x.min().item()}, max={x.max().item()}, mean={x.mean().item()}")

        x1 = self.stem(x)
        x2 = self.encoder1(x1)
        x3 = self.encoder2(x2)
        x4 = self.encoder3(x3)
        x4 = self.bottleneck(x4)
        d3 = self.cross_attn1(self.decoder3(x4), x3)
        d2 = self.cross_attn2(self.decoder2(d3), x2)
        d1 = self.decoder1(d2)

        heatmap = self.heatmap_head(d1)
        flow = self.flow_head(d1)
        scale = self.scale_head(d1)

        # Debug: check outputs for NaN/Inf
        for i, out in enumerate([heatmap, flow, scale]):
            if not torch.isfinite(out).all():
                print(f"[DEBUG] NaN or Inf detected in model output {i}!")
                print(f"Output {i} stats: min={out.min().item()}, max={out.max().item()}, mean={out.mean().item()}")

        return heatmap, flow, scale

class DiceLoss(nn.Module):
    def __init__(self, smooth=1.0):
        super().__init__()
        self.smooth = smooth

    def forward(self, inputs, targets):
        inputs = inputs.reshape(-1)
        targets = targets.reshape(-1)
        intersection = (inputs * targets).sum()
        denom = inputs.sum() + targets.sum() + self.smooth
        if denom == 0 or not torch.isfinite(denom):
            print("[DEBUG] DiceLoss denominator is zero or not finite!")
            return torch.tensor(1.0, device=inputs.device)
        return 1 - (2. * intersection + self.smooth) / denom

class CombinedLoss(nn.Module):
    def __init__(self, alpha=0.5, beta=0.2, gamma=0.3):
        super().__init__()
        self.alpha = alpha
        self.beta = beta
        self.gamma = gamma
        self.dice_loss = DiceLoss()

    def forward(self, outputs, targets):
        heatmap, flow, scale = outputs
        targets_semantic = (targets[:, 0:1, :, :] > 0).float()

        num_pos = targets_semantic.sum()
        num_neg = (targets_semantic <= 0.5).sum()
        total = num_pos + num_neg + 1e-6
        pos_weight = torch.clamp(total / (2 * num_pos + 1e-6), max=20.0)
        if not torch.isfinite(pos_weight).all():
            print("[DEBUG] pos_weight is not finite!", pos_weight)
            pos_weight = torch.ones_like(pos_weight)

        bce_loss = F.binary_cross_entropy_with_logits(
            heatmap, targets_semantic,
            pos_weight=pos_weight,
            reduction='mean'
        )
        if not torch.isfinite(bce_loss):
            print("[DEBUG] BCE loss is not finite!", bce_loss)
            bce_loss = torch.tensor(1.0, device=heatmap.device)

        dice_loss = self.dice_loss(torch.sigmoid(heatmap), targets_semantic)
        flow_loss = flow.pow(2).mean()
        scale_loss = (scale - 1.0).pow(2).mean()

        for name, val in zip(['dice_loss','flow_loss','scale_loss'], [dice_loss, flow_loss, scale_loss]):
            if not torch.isfinite(val):
                print(f"[DEBUG] {name} is not finite!", val)

        total_loss = self.alpha * bce_loss + self.beta * dice_loss + self.gamma * flow_loss + 0.1 * scale_loss
        if not torch.isfinite(total_loss):
            print("[DEBUG] Total loss is not finite!", total_loss)
            total_loss = torch.tensor(1.0, device=heatmap.device)

        return total_loss, {
            'bce_loss': bce_loss.item() if torch.is_tensor(bce_loss) else bce_loss,
            'dice_loss': dice_loss.item() if torch.is_tensor(dice_loss) else dice_loss,
            'flow_loss': flow_loss.item() if torch.is_tensor(flow_loss) else flow_loss,
            'scale_loss': scale_loss.item() if torch.is_tensor(scale_loss) else scale_loss,
            'total_loss': total_loss.item() if torch.is_tensor(total_loss) else total_loss
        }


def train_model(model, train_loader, val_loader, criterion, num_epochs=100, patience=10):
    import gc, time
    import numpy as np
    from tqdm import tqdm
    import torch.nn.functional as F
    import torch.cuda.amp as amp

    device = next(model.parameters()).device
    optimizer = torch.optim.AdamW(model.parameters(), lr=1e-5, weight_decay=0.05)
    scheduler = torch.optim.lr_scheduler.OneCycleLR(
        optimizer,
        max_lr=3e-4,
        total_steps=num_epochs * len(train_loader),
        pct_start=0.1,
        anneal_strategy='cos',
        cycle_momentum=False
    )
    scaler = amp.GradScaler()

    best_val_loss = float('inf')
    epochs_without_improvement = 0
    model_save_path = 'best_spot_detection_model.pth'
    history = {k: [] for k in ['train_loss', 'val_loss', 'precision', 'recall', 'f1', 'inference_time']}

    def safe_metrics(y_true, y_pred):
        y_true = np.array(y_true)
        y_pred = np.array(y_pred)
        if y_true.size == 0 or y_pred.size == 0:
            return 0, 0, 0, 0
        if np.unique(y_true).size < 2 or np.unique(y_pred).size < 2:
            return 0, 0, 0, 0
        from sklearn.metrics import precision_score, recall_score, f1_score, jaccard_score
        precision = precision_score(y_true, y_pred, zero_division=0)
        recall = recall_score(y_true, y_pred, zero_division=0)
        f1 = f1_score(y_true, y_pred, zero_division=0)
        jaccard = jaccard_score(y_true, y_pred, zero_division=0)
        return precision, recall, f1, jaccard

    for epoch in range(num_epochs):
        start_time = time.time()
        model.train()
        running_loss = 0.0
        optimizer.zero_grad()

        train_loader_tqdm = tqdm(train_loader, desc=f"[Train {epoch+1}/{num_epochs}]", dynamic_ncols=True)
        for batch_idx, (images, masks) in enumerate(train_loader_tqdm):
            images = images.to(device, non_blocking=True)
            masks = masks.to(device, non_blocking=True)
            if masks.sum() == 0:
                continue  # skip empty masks batch

            assert torch.isfinite(images).all(), "NaN or Inf in input images"
            assert torch.isfinite(masks).all(), "NaN or Inf in input masks"

            with torch.cuda.amp.autocast(enabled=True):
                outputs = model(images)
                for i, out in enumerate(outputs):
                    if not torch.isfinite(out).all():
                        raise RuntimeError(f"NaN or Inf detected in output {i} during training")
                loss, loss_components = criterion(outputs, masks)

            if not torch.isfinite(loss):
                print("\n🛑 NaN detected in loss during training!")
                print("Loss components:", {k: (v.item() if torch.is_tensor(v) else v) for k, v in loss_components.items()})
                raise ValueError("NaN in training loss")

            scaler.scale(loss).backward()
            scaler.unscale_(optimizer)
            torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=0.5)
            scaler.step(optimizer)
            scaler.update()
            optimizer.zero_grad()
            scheduler.step()

            running_loss += loss.item()
            train_loader_tqdm.set_postfix({
                "Loss": f"{loss.item():.4f}",
                "BCE": f"{loss_components['bce_loss']:.4f}",
                "Dice": f"{loss_components['dice_loss']:.4f}"
            })

            if batch_idx % 100 == 0 and batch_idx > 0:
                torch.cuda.empty_cache()

        train_loss = running_loss / len(train_loader)
        history['train_loss'].append(train_loss)

        model.eval()
        val_loss = 0.0
        inference_times = []
        all_preds, all_labels = [], []
        val_bce_loss = val_dice_loss = val_flow_loss = val_scale_loss = 0.0

        val_loader_tqdm = tqdm(val_loader, desc=f"[Val   {epoch+1}/{num_epochs}]", dynamic_ncols=True)
        with torch.no_grad():
            for batch_idx, (images, masks) in enumerate(val_loader_tqdm):
                images = images.to(device, non_blocking=True)
                masks = masks.to(device, non_blocking=True)
                if masks.sum() == 0:
                    continue  # skip empty masks batch
            
                assert torch.isfinite(images).all(), "NaN or Inf in val images"
                assert torch.isfinite(masks).all(), "NaN or Inf in val masks"

                start_inf = time.time()
                with torch.cuda.amp.autocast(enabled=True):
                    outputs = model(images)
                    # Clamp logits/output to prevent Inf/NaN issues
                    outputs = [out.clamp(min=-20, max=20) for out in outputs]
                inference_times.append(time.time() - start_inf)

                for i, out in enumerate(outputs):
                    if not torch.isfinite(out).all():
                        raise RuntimeError(f"NaN or Inf detected in val output {i} after clamping")

                loss, loss_components = criterion(outputs, masks)
                if not torch.isfinite(loss):
                    print("\n🛑 NaN detected in val loss!")
                    raise ValueError("NaN in validation loss")

                val_loss += loss.item()
                val_bce_loss += loss_components['bce_loss']
                val_dice_loss += loss_components['dice_loss']
                val_flow_loss += loss_components['flow_loss']
                val_scale_loss += loss_components['scale_loss']

                val_loader_tqdm.set_postfix({
                    "Loss": f"{loss.item():.4f}",
                    "BCE": f"{loss_components['bce_loss']:.4f}",
                    "Dice": f"{loss_components['dice_loss']:.4f}"
                })

                if batch_idx % 100 == 0 and batch_idx > 0:
                    torch.cuda.empty_cache()

        val_loss /= len(val_loader)
        val_bce_loss /= len(val_loader)
        val_dice_loss /= len(val_loader)
        val_flow_loss /= len(val_loader)
        val_scale_loss /= len(val_loader)
        avg_inf_time = np.mean(inference_times) * 1000 if inference_times else 0

        if epoch % 10 == 0 or epoch == num_epochs - 1:
            heatmap_pred = (torch.sigmoid(outputs[0].clamp(min=-20, max=20)) > 0.5).int()
            heatmap_true = (masks[:, 0:1, :, :] > 0).int()
            all_preds.extend(heatmap_pred.cpu().numpy().flatten())
            all_labels.extend(heatmap_true.cpu().numpy().flatten())
            precision, recall, f1, jaccard = safe_metrics(all_labels, all_preds)
        else:
            precision = recall = f1 = jaccard = 0.0

        history['val_loss'].append(val_loss)
        history['precision'].append(precision)
        history['recall'].append(recall)
        history['f1'].append(f1)
        history['inference_time'].append(avg_inf_time)

        tqdm.write(
            f"[Epoch {epoch+1:03}/{num_epochs}] "
            f"TrainLoss: {train_loss:.4f} | ValLoss: {val_loss:.4f} "
            f"| BCE: {val_bce_loss:.4f} | Dice: {val_dice_loss:.4f} "
            f"| Flow: {val_flow_loss:.4f} | Scale: {val_scale_loss:.4f} "
            f"| Prec: {precision:.4f} | Rec: {recall:.4f} | F1: {f1:.4f} | Jac: {jaccard:.4f} "
            f"| Inference: {avg_inf_time:.2f} ms | Time: {time.time() - start_time:.2f}s"
        )

        if val_loss < best_val_loss:
            best_val_loss = val_loss
            torch.save({
                'epoch': epoch,
                'model_state_dict': model.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'loss': val_loss,
                'f1_score': f1,
                'history': history
            }, model_save_path)
            tqdm.write("\u2705 Best model saved.")
            epochs_without_improvement = 0
        else:
            epochs_without_improvement += 1
            if epochs_without_improvement >= patience:
                tqdm.write(f"\u23F9\ufe0f Early stopping after {epoch+1} epochs.")
                break

        gc.collect()
        torch.cuda.empty_cache()

    checkpoint = torch.load(model_save_path)
    model.load_state_dict(checkpoint['model_state_dict'])
    tqdm.write(f"\u2705 Loaded best model from epoch {checkpoint['epoch']} with ValLoss {checkpoint['loss']:.4f}")
    return model, checkpoint['history']



def detect_spots(model, image_path, patch_size=256, overlap=0.2, batch_size=4):
    """
    Optimized spot detection for large images with:
    - Overlapping patch processing
    - Batch inference for efficiency
    - Smart patch merging strategy
    """
    image = tifffile.imread(image_path)
    image = image.astype(np.float32)
    if np.max(image) > 0:
        image = image / np.max(image)

    h, w = image.shape

    # Calculate patch parameters
    stride = int(patch_size * (1 - overlap))
    num_patches_x = (w - patch_size) // stride + 1
    num_patches_y = (h - patch_size) // stride + 1

    # Initialize output maps
    heatmap = np.zeros((h, w), dtype=np.float32)
    flow_x = np.zeros((h, w), dtype=np.float32)
    flow_y = np.zeros((h, w), dtype=np.float32)
    scale_map = np.zeros((h, w), dtype=np.float32)
    count_map = np.zeros((h, w), dtype=np.float32)

    model.eval()
    with torch.no_grad():
        # Process patches in batches for efficiency
        patches = []
        positions = []

        for i in range(num_patches_y):
            for j in range(num_patches_x):
                x_start = i * stride
                y_start = j * stride

                # Handle edge cases
                if x_start + patch_size > h:
                    x_start = h - patch_size
                if y_start + patch_size > w:
                    y_start = w - patch_size

                # Extract patch
                patch = image[x_start:x_start+patch_size, y_start:y_start+patch_size]
                if patch.shape[0] != patch_size or patch.shape[1] != patch_size:
                    continue  # Skip incomplete edge patches

                patches.append(patch)
                positions.append((x_start, y_start))

                # Process batch if we have enough patches
                if len(patches) == batch_size or (i == num_patches_y-1 and j == num_patches_x-1):
                    # Convert batch to tensor
                    batch = torch.stack([
                        torch.FloatTensor(p).unsqueeze(0) for p in patches
                    ]).to(device)

                    # Get predictions
                    with torch.cuda.amp.autocast():
                        batch_heatmap, batch_flow, batch_scale = model(batch)

                    # Process each patch in the batch
                    for k, (x_s, y_s) in enumerate(positions[-len(patches):]):
                        # Convert predictions to numpy
                        patch_heatmap = torch.sigmoid(batch_heatmap[k]).squeeze().cpu().numpy()
                        patch_flow = batch_flow[k].squeeze().cpu().numpy().transpose(1, 2, 0)
                        patch_scale = batch_scale[k].squeeze().cpu().numpy()

                        # Add to the full-size outputs with blending in overlapping regions
                        heatmap[x_s:x_s+patch_size, y_s:y_s+patch_size] += patch_heatmap
                        flow_x[x_s:x_s+patch_size, y_s:y_s+patch_size] += patch_flow[..., 0]
                        flow_y[x_s:x_s+patch_size, y_s:y_s+patch_size] += patch_flow[..., 1]
                        scale_map[x_s:x_s+patch_size, y_s:y_s+patch_size] += patch_scale
                        count_map[x_s:x_s+patch_size, y_s:y_s+patch_size] += 1

                    # Clear the current batch
                    patches = []
                    positions = []

    # Normalize overlapping regions with smart blending
    valid_pixels = count_map > 0
    if np.any(valid_pixels):
        # Use average for most regions but weighted blending in overlap areas
        # where count > 1
        blending_weights = np.ones_like(count_map)
        blending_weights[count_map > 1] = 0.5  # Give less weight to overlap regions

        heatmap[valid_pixels] /= count_map[valid_pixels]
        flow_x[valid_pixels] /= count_map[valid_pixels]
        flow_y[valid_pixels] /= count_map[valid_pixels]
        scale_map[valid_pixels] /= count_map[valid_pixels]

    # Combine flow components
    flow = np.stack([flow_x, flow_y], axis=-1)

    return heatmap, flow, scale_map

def get_instance_segmentation(heatmap, flow, scale, threshold=0.5, min_distance=5):
    """
    Optimized instance segmentation with:
    - Adaptive thresholding based on scale map
    - Non-maximum suppression for overlapping spots
    - Size filtering based on scale predictions
    """
    from skimage.feature import peak_local_max
    from scipy.ndimage import distance_transform_edt, label
    from skimage.morphology import watershed, disk
    from skimage.segmentation import random_walker
    import pandas as pd

    # Adaptive thresholding based on scale map
    adaptive_threshold = threshold * (1 + 0.5*(1 - np.mean(scale)))

    # Threshold the heatmap with adaptive threshold
    binary_mask = heatmap > adaptive_threshold

    # Find local maxima in the heatmap
    avg_scale = np.mean(scale[binary_mask])
    adjusted_min_distance = max(3, min(min_distance / avg_scale, 20))

    # Use scale map to adjust peak detection parameters
    peak_threshold = adaptive_threshold * 0.7

    try:
        coordinates = peak_local_max(
            heatmap,
            min_distance=adjusted_min_distance,
            threshold_abs=peak_threshold,
            exclude_border=False
        )
    except:
        coordinates = np.zeros((0, 2), dtype=int)

    if len(coordinates) == 0:
        return np.zeros_like(heatmap, dtype=np.int32), {}

    # Sort coordinates by confidence (heatmap value)
    coordinates = coordinates[np.argsort(-heatmap[coordinates[:,0], coordinates[:,1]])]

    # Create initial instance map
    instance_map = np.zeros_like(heatmap, dtype=np.int32)
    spot_properties = {}

    # Create a distance map for watershed segmentation
    distance_map = distance_transform_edt(~binary_mask)
    distance_map[~binary_mask] = 0

    # Use watershed to better separate close spots
    local_max = np.zeros_like(heatmap, dtype=bool)
    local_max[tuple(coordinates.T)] = True
    markers = label(local_max)
    labels = watershed(-distance_map, markers, mask=binary_mask)

    # If watershed fails, fall back to original method
    if np.max(labels) == 0:
        labels = np.zeros_like(heatmap, dtype=np.int32)
        for spot_id, coord in enumerate(coordinates, start=1):
            y, x = coord
            if instance_map[y, x] > 0:
                continue

            dy, dx = flow[y, x]
            x_adj = x + dx
            y_adj = y + dy
            spot_scale = scale[y, x]
            radius = max(2, adjusted_min_distance * 0.5 * spot_scale)

            yy, xx = np.ogrid[:heatmap.shape[0], :heatmap.shape[1]]
            dist = np.sqrt((xx - x_adj)**2 + (yy - y_adj)**2)
            spot_mask = dist <= radius

            candidate_pixels = (heatmap > adaptive_threshold) & (instance_map == 0) & spot_mask
            if np.sum(candidate_pixels) >= 4:  # Skip if too few pixels
                instance_map[candidate_pixels] = spot_id
                spot_properties[spot_id] = {
                    'position': (float(x_adj), float(y_adj)),
                    'radius': float(radius),
                    'confidence': float(heatmap[y, x]),
                    'scale': float(spot_scale),
                    'flow': (float(dx), float(dy))
                }
    else:
        # Use watershed results
        instance_map = labels
        # Create spot properties from watershed results
        unique_labels = np.unique(instance_map)
        unique_labels = unique_labels[unique_labels != 0]

        for spot_id in unique_labels:
            mask = (instance_map == spot_id)
            y, x = np.unravel_index(np.argmax(heatmap * mask), heatmap.shape)
            dy, dx = flow[y, x]
            x_adj = x + dx
            y_adj = y + dy
            spot_scale = np.mean(scale[mask])
            radius = max(2, adjusted_min_distance * 0.5 * spot_scale)

            spot_properties[int(spot_id)] = {
                'position': (float(x_adj), float(y_adj)),
                'radius': float(radius),
                'confidence': float(np.max(heatmap[mask])),
                'scale': float(spot_scale),
                'flow': (float(dx), float(dy)),
                'area': int(np.sum(mask))
            }

    return instance_map, spot_properties

def visualize_results(image, heatmap, flow, scale, instance_map, spot_properties=None):
    """Visualize detection results with improved layout and annotations."""
    fig, axs = plt.subplots(3, 2, figsize=(20, 18))

    # Plot original image
    axs[0, 0].imshow(image, cmap='gray')
    axs[0, 0].set_title('Original Image')

    # Plot heatmap
    im = axs[0, 1].imshow(heatmap, cmap='hot')
    plt.colorbar(im, ax=axs[0, 1])
    axs[0, 1].set_title('Detection Heatmap')

    # Plot flow field
    axs[1, 0].imshow(image, cmap='gray', alpha=0.7)
    y, x = np.mgrid[0:heatmap.shape[0]:10, 0:heatmap.shape[1]:10]
    axs[1, 0].quiver(x, y, flow[y, x, 1], flow[y, x, 0], color='red', scale=20)
    axs[1, 0].set_title('Optical Flow Field')

    # Plot scale map
    im = axs[1, 1].imshow(scale, cmap='viridis')
    plt.colorbar(im, ax=axs[1, 1])
    axs[1, 1].set_title('Scale Prediction Map')

    # Plot instance segmentation
    axs[2, 0].imshow(image, cmap='gray', alpha=0.7)
    instance_img = axs[2, 0].imshow(instance_map, cmap='tab20', alpha=0.5)
    axs[2, 0].set_title('Instance Segmentation')

    # Plot spot properties if available
    if spot_properties:
        axs[2, 1].set_xlim(0, image.shape[1])
        axs[2, 1].set_ylim(image.shape[0], 0)
        axs[2, 1].set_title('Detected Spots with Properties')

        spots = list(spot_properties.values())
        if spots:
            sizes = [s['radius']*2 for s in spots]
            positions = [(s['position'][0], s['position'][1]) for s in spots]
            colors = [(s['confidence'], 0, 1-s['confidence']) for s in spots]

            sc = axs[2, 1].scatter(*zip(*positions), s=sizes, c=colors, alpha=0.7)
            axs[2, 1].set_xlabel('X Position')
            axs[2, 1].set_ylabel('Y Position')
        else:
            axs[2, 1].text(0.5, 0.5, 'No spots detected',
                         horizontalalignment='center',
                         verticalalignment='center',
                         transform=axs[2, 1].transAxes)
    else:
        axs[2, 1].axis('off')

    plt.tight_layout()
    plt.show()
    
# %% [markdown]
# ## 7. Main Execution





# %% [markdown]
# ## 3. Training Function (with progress display)

# %%
def train_notebook():
    """Training function adapted for notebook environment."""
    # Load and prepare data
    image_paths = sorted(glob.glob(os.path.join(INPUT_DIR, '*.tif')))
    mask_paths = sorted(glob.glob(os.path.join(MASK_DIR or INPUT_DIR, '*.tif')))

    if not image_paths:
        raise ValueError(f"No images found in {INPUT_DIR}")
    if MODE != 'train' and not mask_paths and MASK_DIR:
        raise ValueError(f"No masks found in {MASK_DIR}")

    # Split data
    train_imgs, val_imgs, train_masks, val_masks = train_test_split(
        image_paths, mask_paths, test_size=0.2, random_state=42
    )

    # Create datasets and loaders with optimized parameters
    transform = transforms.Compose([
        transforms.RandomHorizontalFlip(),
        transforms.RandomVerticalFlip(),
        transforms.RandomRotation((0, 180)),
        transforms.ColorJitter(brightness=0.1, contrast=0.1, saturation=0.1, hue=0.05),
        transforms.ToTensor(),
    ])

    num_workers = min(8, os.cpu_count() or 4)

    train_dataset = MicroscopyDataset(train_imgs, train_masks, transform=transform)
    val_dataset = MicroscopyDataset(val_imgs, val_masks, transform=None)

    # Create weighted sampler for training
    sampler = WeightedRandomSampler(
        train_dataset.weights,
        len(train_dataset.weights),
        replacement=True
    )

    # Create data loaders
    train_loader = DataLoader(
        train_dataset,
        batch_size=BATCH_SIZE,
        sampler=sampler,
        num_workers=num_workers,
        pin_memory=True,
        prefetch_factor=2,
        persistent_workers=True
    )
    val_loader = DataLoader(
        val_dataset,
        batch_size=BATCH_SIZE,
        shuffle=False,
        num_workers=num_workers,
        pin_memory=True,
        prefetch_factor=2,
        persistent_workers=True
    )

    # Initialize model
    model = SpotDetector2025().to(device)
    
    criterion = CombinedLoss()

    # Train model with progress display in notebook
    trained_model, history = train_model(
        model, train_loader, val_loader, criterion,
        num_epochs=EPOCHS, patience=10
    )

    # Save model
    torch.save({
        'model_state_dict': trained_model.state_dict(),
        'history': history
    }, MODEL_PATH)

    # Plot training history
    plt.figure(figsize=(12, 8))
    plt.subplot(2, 2, 1)
    plt.plot(history['train_loss'], label='Train Loss')
    plt.plot(history['val_loss'], label='Val Loss')
    plt.title('Training and Validation Loss')
    plt.legend()

    plt.subplot(2, 2, 2)
    plt.plot(history['precision'], label='Precision')
    plt.plot(history['recall'], label='Recall')
    plt.plot(history['f1'], label='F1 Score')
    plt.title('Validation Metrics')
    plt.legend()

    plt.subplot(2, 2, 3)
    plt.plot(history['inference_time'])
    plt.title('Inference Time per Batch')

    plt.subplot(2, 2, 4)
    plt.plot(history['f1'], label='F1 Score')
    plt.plot(history['iou'], label='IoU')
    plt.title('Segmentation Metrics')
    plt.legend()

    plt.tight_layout()
    plt.show()

    return trained_model, history

# %% [markdown]
# ## 4. Inference Function (Notebook Version)



 ##%% [markdown]
# ## 7. Main Execution in Notebook

# %%
# Notebook execution - choose which function to run by uncommenting the appropriate line
# Remember to set the parameters in the Configuration cell above!

# %% [markdown]
# ## 2. Configuration
# Define all parameters as notebook variables instead of command-line arguments

## %%
# Configuration parameters
MODE = 'train'  # Options: 'train', 'inference', 'batch_inference', 'evaluate'
MODEL_PATH = '/mnt/d/Users/<USER>/Documents/spot detector git amazon 2025/training_dataset/best_spot_detection_model.pth'
INPUT_DIR = "/mnt/d/Users/<USER>/FISH_spots/2d/image_raw/large spots/images/"
OUTPUT_DIR = 'detection_results'
TEST_IMAGE = "/mnt/d/Users/<USER>/FISH_spots/2d/image_raw/large spots/images/005.tif"  # Path to a single test image
MASK_DIR = "/mnt/d/Users/<USER>/FISH_spots/2d/image_raw/large spots/masks/"
EPOCHS = 400
BATCH_SIZE =  2
os.environ["PYTORCH_CUDA_ALLOC_CONF"] = "expandable_segments:True"


def run_inference_notebook(model_path=MODEL_PATH, test_image_path=TEST_IMAGE, output_dir=OUTPUT_DIR):
    """Run inference on a single test image with notebook-friendly output."""
    if not test_image_path:
        print("Please set TEST_IMAGE variable to the path of your test image")
        return

    # Load the trained model
    checkpoint = torch.load(model_path)
    model = SpotDetector2025().to(device)
    model.load_state_dict(checkpoint['model_state_dict'])
   
    # Run detection
    heatmap, flow, scale = detect_spots(model, test_image_path)
    instance_map, spot_props = get_instance_segmentation(heatmap, flow, scale)
    test_image = tifffile.imread(test_image_path)

    # Visualize results interactively
    visualize_results(test_image, heatmap, flow, scale, instance_map, spot_props)

    # Display spot properties in a table if available
    if spot_props:
        from pandas import DataFrame
        df = DataFrame.from_dict(spot_props, orient='index')
        display(df.describe())
        display(df.head())

    # Save results to disk
    os.makedirs(output_dir, exist_ok=True)
    base_name = os.path.splitext(os.path.basename(test_image_path))[0]

    output_paths = {
        'heatmap': os.path.join(output_dir, f'{base_name}_heatmap.tif'),
        'flow': os.path.join(output_dir, f'{base_name}_flow.tif'),
        'scale': os.path.join(output_dir, f'{base_name}_scale.tif'),
        'instance': os.path.join(output_dir, f'{base_name}_instance.tif'),
        'visualization': os.path.join(output_dir, f'{base_name}_visualization.png'),
        'props': os.path.join(output_dir, f'{base_name}_props.json')
    }

    # Save numerical outputs
    instance_map = np.clip(instance_map, np.iinfo(np.int32).min, np.iinfo(np.int32).max)
    tifffile.imwrite(output_paths['heatmap'], heatmap.astype(np.float32))
    tifffile.imwrite(output_paths['flow'], flow.astype(np.float32))
    tifffile.imwrite(output_paths['scale'], scale.astype(np.float32))
    tifffile.imwrite(output_paths['instance'], instance_map.astype(np.int32))

    # Save visualization
    plt.figure(figsize=(20, 15))
    plt.subplot(2, 2, 1)
    plt.imshow(test_image, cmap='gray')
    plt.title('Original Image')

    plt.subplot(2, 2, 2)
    plt.imshow(heatmap, cmap='hot')
    plt.title('Detection Heatmap')

    plt.subplot(2, 2, 3)
    plt.imshow(instance_map, cmap='tab20')
    plt.title('Instance Segmentation')

    plt.subplot(2, 2, 4)
    if spot_props:
        spots = list(spot_props.values())
        positions = [(s['position'][0], s['position'][1]) for s in spots]
        sizes = [s['radius']*2 for s in spots]
        colors = [(s['confidence'], 0, 1-s['confidence']) for s in spots]
        plt.scatter(*zip(*positions), s=sizes, c=colors, alpha=0.7)
        plt.xlim(0, test_image.shape[1])
        plt.ylim(test_image.shape[0], 0)
        plt.title('Detected Spots')
    plt.tight_layout()
    plt.savefig(output_paths['visualization'], dpi=300)
    plt.close()

    # Save spot properties
    if spot_props:
        with open(output_paths['props'], 'w') as f:
            json.dump(spot_props, f, indent=2)

    print(f"Results saved to {output_dir}")
    return output_paths

# %% [markdown]
# ## 5. Batch Inference Function (Notebook Version)

# %%
def batch_inference_notebook(model_path=MODEL_PATH, input_dir=INPUT_DIR, output_dir=OUTPUT_DIR):
    """Process multiple images in batch mode with progress display."""
    # Load model once and reuse
    checkpoint = torch.load(model_path)
    model = SpotDetector2025().to(device)
    model.load_state_dict(checkpoint['model_state_dict'])
    model.eval()

    # Get list of input images
    image_paths = sorted(glob.glob(os.path.join(input_dir, '*.tif')))
    if not image_paths:
        raise ValueError(f"No images found in {input_dir}")

    # Create output directory
    os.makedirs(output_dir, exist_ok=True)

    all_outputs = []
    total_images = len(image_paths)

    # Create a progress bar
    from tqdm import tqdm
    for i, img_path in enumerate(tqdm(image_paths, desc="Processing images")):
        try:
            # Display progress
            clear_output(wait=True)
            print(f"Processing image {i+1}/{total_images}: {os.path.basename(img_path)}")

            # Process image
            heatmap, flow, scale = detect_spots(model, img_path)
            instance_map, spot_props = get_instance_segmentation(heatmap, flow, scale)
            test_image = tifffile.imread(img_path)

            # Get base filename
            base_name = os.path.splitext(os.path.basename(img_path))[0]

            # Define output paths for this image
            output_paths = {
                'heatmap': os.path.join(output_dir, f'{base_name}_heatmap.tif'),
                'flow': os.path.join(output_dir, f'{base_name}_flow.tif'),
                'scale': os.path.join(output_dir, f'{base_name}_scale.tif'),
                'instance': os.path.join(output_dir, f'{base_name}_instance.tif'),
                'visualization': os.path.join(output_dir, f'{base_name}_visualization.png'),
                'props': os.path.join(output_dir, f'{base_name}_props.json')
            }

            # Save results
            instance_map = np.clip(instance_map, np.iinfo(np.int32).min, np.iinfo(np.int32).max)
            tifffile.imwrite(output_paths['heatmap'], heatmap.astype(np.float32))
            tifffile.imwrite(output_paths['flow'], flow.astype(np.float32))
            tifffile.imwrite(output_paths['scale'], scale.astype(np.float32))
            tifffile.imwrite(output_paths['instance'], instance_map.astype(np.int32))

            # Save visualization
            plt.figure(figsize=(20, 15))
            plt.subplot(2, 2, 1)
            plt.imshow(test_image, cmap='gray')
            plt.title('Original Image')

            plt.subplot(2, 2, 2)
            plt.imshow(heatmap, cmap='hot')
            plt.title('Detection Heatmap')

            plt.subplot(2, 2, 3)
            plt.imshow(instance_map, cmap='tab20')
            plt.title('Instance Segmentation')

            plt.subplot(2, 2, 4)
            if spot_props:
                spots = list(spot_props.values())
                positions = [(s['position'][0], s['position'][1]) for s in spots]
                sizes = [s['radius']*2 for s in spots]
                colors = [(s['confidence'], 0, 1-s['confidence']) for s in spots]
                plt.scatter(*zip(*positions), s=sizes, c=colors, alpha=0.7)
                plt.xlim(0, test_image.shape[1])
                plt.ylim(test_image.shape[0], 0)
                plt.title('Detected Spots')
            plt.tight_layout()
            plt.savefig(output_paths['visualization'], dpi=300)
            plt.close()

            # Save spot properties
            if spot_props:
                with open(output_paths['props'], 'w') as f:
                    json.dump(spot_props, f, indent=2)

            all_outputs.append(output_paths)

            # Display sample results occasionally
            if i % 5 == 0 or i == total_images-1:
                display(plt.gcf())
                plt.close()

        except Exception as e:
            print(f"Error processing {img_path}: {str(e)}")
            continue

    print(f"Batch processing complete. Results saved to {output_dir}")
    return all_outputs

# %% [markdown]
# ## 6. Evaluation Function (Notebook Version)

# %%
def evaluate_model_notebook(model_path=MODEL_PATH, input_dir=INPUT_DIR, mask_dir=MASK_DIR, output_dir=OUTPUT_DIR):
    """Evaluate model performance with notebook-friendly output."""
    # Load model
    checkpoint = torch.load(model_path)
    model = SpotDetector2025().to(device)
    model.load_state_dict(checkpoint['model_state_dict'])
    model.eval()

    # Get test images
    test_images = sorted(glob.glob(os.path.join(input_dir, '*.tif')))
    test_masks = None

    if mask_dir:
        test_masks = sorted(glob.glob(os.path.join(mask_dir, '*.tif')))
        if len(test_images) != len(test_masks):
            raise ValueError("Number of test images and masks doesn't match")

    # Initialize metrics storage
    metrics = {
        'precision': [],
        'recall': [],
        'f1': [],
        'iou': [],
        'detection_rate': [],
        'false_positives': [],
        'false_negatives': [],
        'inference_times': []
    }

    # Create progress bar
    from tqdm import tqdm
    total_images = len(test_images)

    for i, (img_path, mask_path) in enumerate(tqdm(zip(test_images, test_masks or []), total=total_images, desc="Evaluating")):
        try:
            start_time = time.time()

            # Process image
            heatmap, flow, scale = detect_spots(model, img_path)
            instance_map, spot_props = get_instance_segmentation(heatmap, flow, scale)
            pred_mask = (heatmap > 0.5).astype(np.uint8)

            inference_time = time.time() - start_time
            metrics['inference_times'].append(inference_time)

            if mask_dir:  # If ground truth available
                mask = tifffile.imread(mask_path)

                # Calculate metrics
                precision = precision_score(mask.flatten(), pred_mask.flatten(), zero_division=0)
                recall = recall_score(mask.flatten(), pred_mask.flatten(), zero_division=0)
                f1 = f1_score(mask.flatten(), pred_mask.flatten(), zero_division=0)
                iou = jaccard_score(mask.flatten(), pred_mask.flatten(), zero_division=0)

                metrics['precision'].append(precision)
                metrics['recall'].append(recall)
                metrics['f1'].append(f1)
                metrics['iou'].append(iou)

                # Count detected spots vs ground truth
                pred_spots = len(np.unique(instance_map)) - 1
                true_spots = len(np.unique(mask)) - 1

                if true_spots > 0:
                    detection_rate = min(pred_spots / true_spots, 1.0)
                    fp = max(0, pred_spots - true_spots)
                    fn = max(0, true_spots - pred_spots)
                else:
                    detection_rate = 1.0 if pred_spots == 0 else 0.0
                    fp = pred_spots
                    fn = 0

                metrics['detection_rate'].append(detection_rate)
                metrics['false_positives'].append(fp)
                metrics['false_negatives'].append(fn)

                # Display progress occasionally
                if i % 5 == 0 or i == total_images-1:
                    clear_output(wait=True)
                    plt.figure(figsize=(12, 6))
                    plt.subplot(1, 3, 1)
                    plt.imshow(mask, cmap='gray')
                    plt.title(f'Ground Truth ({true_spots} spots)')

                    plt.subplot(1, 3, 2)
                    plt.imshow(pred_mask, cmap='gray')
                    plt.title(f'Prediction ({pred_spots} spots)')

                    plt.subplot(1, 3, 3)
                    plt.imshow(mask, cmap='gray', alpha=0.5)
                    plt.imshow(pred_mask, cmap='hot', alpha=0.5)
                    plt.title('Overlay')
                    plt.suptitle(f"Image {i+1}/{total_images}")
                    plt.tight_layout()
                    plt.show()

        except Exception as e:
            print(f"Error evaluating {img_path}: {str(e)}")
            continue

    # Calculate and display average metrics
    avg_metrics = {}
    if metrics['precision']:
        avg_metrics = {k: np.mean(v) for k, v in metrics.items() if v}
        avg_metrics['avg_inference_time'] = np.mean(metrics['inference_times']) if metrics['inference_times'] else 0

    # Display results
    clear_output(wait=True)
    if avg_metrics:
        print("Evaluation Metrics:")
        for k, v in sorted(avg_metrics.items()):
            print(f"{k.replace('_', ' ').title()}: {v:.4f}")

        # Plot metrics
        plt.figure(figsize=(12, 8))

       

        plt.subplot(2, 2, 1)
        plt.bar(['Precision', 'Recall', 'F1', 'IoU'],
                [avg_metrics['precision'], avg_metrics['recall'],
                 avg_metrics['f1'], avg_metrics['iou']])
        plt.title('Classification Metrics')
        plt.ylim(0, 1)

        plt.subplot(2, 2, 2)
        plt.bar(['Detection Rate'], [avg_metrics['detection_rate']])
        plt.title('Detection Metrics')
        plt.ylim(0, 1)

        plt.subplot(2, 2, 3)
        plt.bar(['False Positives', 'False Negatives'],
                [np.mean(metrics['false_positives']), np.mean(metrics['false_negatives'])])
        plt.title('Error Analysis')

        plt.subplot(2, 2, 4)
        plt.hist(metrics['inference_times'], bins=20)
        plt.title('Inference Time Distribution')
        plt.xlabel('Time (s)')
        plt.ylabel('Count')

        plt.tight_layout()
        plt.show()
    else:
        print("Metrics calculation completed (no ground truth provided for full metrics)")

    # Save metrics to file
    if avg_metrics:
        os.makedirs(output_dir, exist_ok=True)
        with open(os.path.join(output_dir, 'metrics.json'), 'w') as f:
            json.dump(avg_metrics, f, indent=2)
        print(f"Metrics saved to {output_dir}/metrics.json")

    return avg_metrics

#



# Enhanced execution logic with auto-training
if MODE != 'train':
    # Check if model exists, train if not
    if not os.path.exists(MODEL_PATH):
        print(f"Model file not found at {MODEL_PATH}. Training a new model first...")
        MODE = 'train'  # Switch to training mode temporarily
        trained_model, history = train_notebook()
        print("Training completed. Proceeding with the selected operation...")

        # Restore the original mode for subsequent operations
        MODE = 'inference'  # Or whatever was originally set

# Now execute the requested mode
if MODE == 'train':
    print("Starting training...")
    trained_model, history = train_notebook()
elif MODE == 'inference' and TEST_IMAGE:
    print("Running inference on single image...")
    output_paths = run_inference_notebook()
elif MODE == 'batch_inference':
    print("Running batch inference...")
    outputs = batch_inference_notebook()
elif MODE == 'evaluate':
    print("Evaluating model...")
    metrics = evaluate_model_notebook()
else:
    print("Please set MODE to one of: 'train', 'inference', 'batch_inference', or 'evaluate'")
    print("And configure the appropriate paths in the Configuration cell.")







