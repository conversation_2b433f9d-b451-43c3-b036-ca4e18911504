{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import sys\n", "sys.path.append('/home/<USER>/.local/lib/python3.10/site-packages')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {"vscode": {"languageId": "markdown"}}, "outputs": [], "source": ["\"Description of the Code\n", "This code performs image alignment for multi-channel TIFF images using both affine and non-rigid registration techniques. The process involves the following steps:\n", "\n", "Loading and Normalizing Images: The code reads the images from the specified source directory, normalizes their intensity values to the range [0,1], and extracts pixel size information from the TIFF metadata.\n", "Resizing Images: Each image is resized to match the pixel size of a reference image to ensure consistent scaling.\n", "Affine Registration: An affine transformation is applied to align the images to the reference image. This step corrects for translation, rotation, scaling, and shearing.\n", "Non-Rigid Registration: A non-rigid transformation using the SyN (Symmetric Normalization) method is applied to further refine the alignment by accounting for local deformations.\n", "Applying Transformations: The computed transformations are applied to all channels of each image set to ensure consistent alignment across channels.\n", "Saving Aligned Images: The aligned images are saved in a multi-channel TIFF stack in the specified output directory.\""]}, {"cell_type": "code", "execution_count": 18, "metadata": {"vscode": {"languageId": "markdown"}}, "outputs": [], "source": ["import os\n", "import re\n", "from pathlib import Path\n", "from typing import List, Dict, Optional, Tuple\n", "import time\n", "from tqdm import tqdm\n", "import gc\n", "\n", "import cupy as cp\n", "import numpy as np\n", "import tifffile\n", "import ants\n", "import matplotlib.pyplot as plt\n", "from skimage.registration import phase_cross_correlation\n", "from skimage.exposure import equalize_adapthist\n", "\n", "def normalize_image(img: cp.ndarray) -> cp.ndarray:\n", "    img_min = img.min()\n", "    img_max = img.max()\n", "    return (img - img_min) / (img_max - img_min + 1e-12)\n", "\n", "def apply_clahe(img: cp.ndarray, kernel_size: Tuple[int, int] = (32, 32)) -> cp.ndarray:\n", "    img_np = cp.asnumpy(img)\n", "    img_eq = equalize_adapthist(img_np, kernel_size=kernel_size)\n", "    return cp.asarray(img_eq)\n", "\n", "def pad_to_largest(images: List[cp.ndarray]) -> List[cp.ndarray]:\n", "    max_h = max(img.shape[0] for img in images)\n", "    max_w = max(img.shape[1] for img in images)\n", "    padded = []\n", "    for img in images:\n", "        pad_h = max_h - img.shape[0]\n", "        pad_w = max_w - img.shape[1]\n", "        pad_top = pad_h // 2\n", "        pad_bottom = pad_h - pad_top\n", "        pad_left = pad_w // 2\n", "        pad_right = pad_w - pad_left\n", "        img_padded = cp.pad(img, ((pad_top, pad_bottom), (pad_left, pad_right)),\n", "                            mode='constant', constant_values=0)\n", "        padded.append(img_padded)\n", "    return padded\n", "\n", "def downsample_image(img: cp.ndarray, factor: int = 2) -> cp.ndarray:\n", "    return img[::factor, ::factor]\n", "\n", "def phase_correlation_shift(fixed: cp.ndarray, moving: cp.ndarray) -> tuple[int, int]:\n", "    F1 = cp.fft.fft2(fixed)\n", "    F2 = cp.fft.fft2(moving)\n", "    R = F1 * cp.conj(F2)\n", "    R /= cp.abs(R) + 1e-8\n", "    r = cp.fft.ifft2(R)\n", "    max_loc = cp.unravel_index(cp.argmax(cp.abs(r)), r.shape)\n", "    shift_y, shift_x = max_loc\n", "    if shift_y > r.shape[0] // 2:\n", "        shift_y -= r.shape[0]\n", "    if shift_x > r.shape[1] // 2:\n", "        shift_x -= r.shape[1]\n", "    return int(shift_y), int(shift_x)\n", "\n", "def affine_initializer_fixed(fixed, moving):\n", "    fixed_np = cp.asnumpy(fixed) if isinstance(fixed, cp.ndarray) else fixed\n", "    moving_np = cp.asnumpy(moving) if isinstance(moving, cp.ndarray) else moving\n", "\n", "    try:\n", "        fixed_ants = ants.from_numpy(fixed_np)\n", "        moving_ants = ants.from_numpy(moving_np)\n", "        tx = ants.initial_transform(fixed=fixed_ants, moving=moving_ants, type_of_transform='Similarity')\n", "        return tx\n", "    except Exception as e:\n", "        print(f\"ANTs initializer failed: {e}\")\n", "\n", "    try:\n", "        shift_y, shift_x, _ = phase_cross_correlation(fixed_np, moving_np, upsample_factor=10)\n", "    except Exception as e:\n", "        print(f\"Phase correlation failed: {e}\")\n", "        shift_y, shift_x = phase_correlation_shift(fixed, moving)\n", "\n", "    tx = ants.new_ants_transform(dimension=2)\n", "    tx.set_parameters([0, 0, 0, 0, -shift_x, -shift_y])\n", "    return tx\n", "\n", "def compute_correlation_similarity(fixed: cp.ndarray, moving: cp.ndarray) -> float:\n", "    fixed_flat = fixed.flatten()\n", "    moving_flat = moving.flatten()\n", "\n", "    fixed_norm = (fixed_flat - fixed_flat.mean()) / (fixed_flat.std() + 1e-8)\n", "    moving_norm = (moving_flat - moving_flat.mean()) / (moving_flat.std() + 1e-8)\n", "\n", "    correlation = cp.corrcoef(fixed_norm, moving_norm)[0, 1]\n", "    corr_val = float(cp.asnumpy(correlation))\n", "    if cp.isnan(correlation) or cp.isinf(correlation):\n", "        return 0.0\n", "    return max(0.0, (corr_val + 1.0) / 2.0)\n", "\n", "def ants_registration_optimized(fixed: cp.ndarray, moving: cp.ndarray,\n", "                               transform_type: str,\n", "                               initializer: Optional[ants.ANTsTransform] = None,\n", "                               interpolator: str = 'linear',\n", "                               downsample_factor: int = 1) -> (cp.<PERSON><PERSON><PERSON>, List[str], float):\n", "\n", "    if downsample_factor > 1:\n", "        fixed_ds = downsample_image(fixed, downsample_factor)\n", "        moving_ds = downsample_image(moving, downsample_factor)\n", "    else:\n", "        fixed_ds = fixed\n", "        moving_ds = moving\n", "\n", "    fixed_ants = ants_from_cp(normalize_image(fixed_ds))\n", "    moving_ants = ants_from_cp(apply_clahe(normalize_image(moving_ds)))\n", "\n", "    if transform_type == \"SyN\":\n", "        reg = ants.registration(\n", "            fixed=fixed_ants,\n", "            moving=moving_ants,\n", "            type_of_transform=transform_type,\n", "            grad_step=0.2,\n", "            flow_sigma=3,\n", "            total_sigma=0,\n", "            syn_metric='CC',\n", "            syn_sampling=16,\n", "            reg_iterations=(40,30,20,10),\n", "            verbose=False\n", "        )\n", "    else:\n", "        reg = ants.registration(\n", "            fixed=fixed_ants,\n", "            moving=moving_ants,\n", "            type_of_transform=transform_type,\n", "            initial_transform=initializer,\n", "            verbose=False\n", "        )\n", "\n", "    fwd_transforms = reg.get('fwdtransforms', [])\n", "    if isinstance(fwd_transforms, str):\n", "        fwd_transforms = [fwd_transforms]\n", "\n", "    warped = ants.apply_transforms(\n", "        fixed=ants_from_cp(normalize_image(fixed)),\n", "        moving=ants_from_cp(normalize_image(moving)),\n", "        transformlist=fwd_transforms,\n", "        interpolator=interpolator\n", "    )\n", "\n", "    similarity = compute_correlation_similarity(ants_to_cp(ants_from_cp(normalize_image(fixed))), ants_to_cp(warped))\n", "\n", "    return ants_to_cp(warped), fwd_transforms, similarity\n", "\n", "def ants_registration_micro(fixed: cp.ndarray, moving: cp.ndarray,\n", "                           interpolator: str = 'linear',\n", "                           downsample_factor: int = 2,\n", "                           timeout: int = 60) -> (cp.<PERSON><PERSON><PERSON>, List[str], float):\n", "\n", "    if downsample_factor > 1:\n", "        fixed_ds = downsample_image(fixed, downsample_factor)\n", "        moving_ds = downsample_image(moving, downsample_factor)\n", "    else:\n", "        fixed_ds = fixed\n", "        moving_ds = moving\n", "\n", "    fixed_ants = ants_from_cp(normalize_image(fixed_ds))\n", "    moving_ants = ants_from_cp(apply_clahe(normalize_image(moving_ds)))\n", "\n", "    start_time = time.time()\n", "\n", "    reg = ants.registration(\n", "        fixed=fixed_ants,\n", "        moving=moving_ants,\n", "        type_of_transform=\"SyN\",\n", "        grad_step=0.5,\n", "        flow_sigma=1,\n", "        total_sigma=0,\n", "        syn_metric='CC',\n", "        syn_sampling=8,\n", "        reg_iterations=(10,5,3),\n", "        verbose=False\n", "    )\n", "\n", "    elapsed = time.time() - start_time\n", "    if elapsed > timeout:\n", "        raise TimeoutError(f\"Micro-registration exceeded {timeout}s timeout\")\n", "\n", "    fwd_transforms = reg.get('fwdtransforms', [])\n", "    if isinstance(fwd_transforms, str):\n", "        fwd_transforms = [fwd_transforms]\n", "\n", "    warped = ants.apply_transforms(\n", "        fixed=ants_from_cp(normalize_image(fixed)),\n", "        moving=ants_from_cp(normalize_image(moving)),\n", "        transformlist=fwd_transforms,\n", "        interpolator=interpolator\n", "    )\n", "\n", "    similarity = compute_correlation_similarity(ants_to_cp(ants_from_cp(normalize_image(fixed))), ants_to_cp(warped))\n", "\n", "    return ants_to_cp(warped), fwd_transforms, similarity\n", "\n", "def apply_shift(img: cp.ndarray, shift: tuple[int, int]) -> cp.ndarray:\n", "    return cp.roll(cp.roll(img, shift[0], axis=0), shift[1], axis=1)\n", "\n", "def ants_from_cp(img: cp.n<PERSON><PERSON>) -> ants.ANTsImage:\n", "    return ants.from_numpy(cp.asnumpy(img))\n", "\n", "def ants_to_cp(img: ants.ANTsImage) -> cp.n<PERSON>ray:\n", "    return cp.asarray(img.numpy())\n", "\n", "def compute_similarity(fixed: cp.ndarray, moving: cp.ndarray) -> float:\n", "    from skimage.metrics import normalized_mutual_information\n", "    return float(normalized_mutual_information(cp.asnumpy(fixed), cp.asnumpy(moving)))\n", "\n", "def apply_transforms_to_channel(channel: cp.ndarray, fixed: cp.ndarray,\n", "                              transformlist: List[str],\n", "                              interpolator: str = 'bSpline') -> cp.ndarray:\n", "    fixed_ants = ants_from_cp(fixed)\n", "    moving_ants = ants_from_cp(channel)\n", "    warped = ants.apply_transforms(\n", "        fixed=fixed_ants,\n", "        moving=moving_ants,\n", "        transformlist=transformlist,\n", "        interpolator=interpolator\n", "    )\n", "    return ants_to_cp(warped)\n", "\n", "def save_image(img: cp.ndarray, output_dir: str, filename: str, original_img: cp.ndarray):\n", "    out_path = os.path.join(output_dir, filename)\n", "\n", "    # Ensure original_img is a CuPy array if it's not already\n", "    if isinstance(original_img, np.ndarray):\n", "        original_img = cp.asarray(original_img)\n", "\n", "    # Perform operations using CuPy methods\n", "    original_min = cp.asnumpy(original_img.min())\n", "    original_max = cp.asnumpy(original_img.max())\n", "    img_np = cp.asnumpy(img) * (original_max - original_min) + original_min\n", "\n", "    tifffile.imwrite(out_path, img_np.astype(np.float32))\n", "    print(f\"Saved image: {out_path}\")\n", "\n", "def plot_overlay(fixed: cp.ndarray, moving: cp.ndar<PERSON>, title: str, alpha=0.5):\n", "    fixed_np = cp.asnumpy(normalize_image(fixed))\n", "    moving_np = cp.asnumpy(normalize_image(moving))\n", "    overlay = np.zeros((*fixed_np.shape, 3), dtype=np.float32)\n", "    overlay[..., 0] = fixed_np\n", "    overlay[..., 1] = moving_np\n", "\n", "    plt.figure(figsize=(10, 10))\n", "    plt.imshow(overlay)\n", "    plt.title(title)\n", "    plt.axis('off')\n", "    plt.show()\n", "\n", "def get_basename_key(filename: str) -> str:\n", "    match = re.match(r\"(.+)_ch\\d{2}\\.tif$\", filename)\n", "    if match:\n", "        return match.group(1)\n", "    raise ValueError(f\"Filename {filename} does not match expected pattern *_chXX.tif\")\n", "\n", "def group_images_by_basename(folder: str) -> Dict[str, Dict[str, str]]:\n", "    files = [f for f in os.listdir(folder) if f.endswith('.tif')]\n", "    groups = {}\n", "    for f in files:\n", "        base = get_basename_key(f)\n", "        ch_match = re.search(r\"_ch(\\d{2})\\.tif$\", f)\n", "        if not ch_match:\n", "            continue\n", "        ch = f\"ch{ch_match.group(1)}\"\n", "        groups.setdefault(base, {})[ch] = os.path.join(folder, f)\n", "    return groups\n", "\n", "def load_channels(paths: Dict[str, str]) -> Dict[str, cp.ndarray]:\n", "    channels = {}\n", "    for ch, p in paths.items():\n", "        img = tifffile.imread(p)\n", "        if img.ndim > 2:\n", "            img = img[0]\n", "        channels[ch] = cp.asarray(img)\n", "    return channels\n", "\n", "def cupy_fourier_shift(img: cp.ndarray, shift: tuple) -> cp.ndarray:\n", "    cp.get_default_memory_pool().free_all_blocks()\n", "    shift_y, shift_x = shift\n", "    return cp.roll(cp.roll(img, int(shift_y), axis=0), int(shift_x), axis=1)\n", "\n", "def cleanup_memory():\n", "    cp.get_default_memory_pool().free_all_blocks()\n", "    gc.collect()\n", "\n", "def batch_align_to_reference_optimized(\n", "    reference_img_path,\n", "    input_folder,\n", "    output_folder,\n", "    affine_transform_type=\"Affine\",\n", "    use_non_rigid=True,\n", "    use_micro_registration=False,\n", "    save_intermediate: bool = False,\n", "    show_overlay: bool = False,\n", "    reg_interpolator=\"linear\",\n", "    apply_interpolator=\"linear\",\n", "    downsample_factor: int = 2,\n", "    similarity_threshold: float = 0.8\n", "):\n", "    print(f\"[INFO] Loading reference ch00 image from {reference_img_path}...\")\n", "    fixed_ch00 = cp.asarray(tifffile.imread(reference_img_path))\n", "\n", "    groups = group_images_by_basename(input_folder)\n", "    print(f\"[INFO] Found {len(groups)} groups to align.\")\n", "\n", "    Path(output_folder).mkdir(parents=True, exist_ok=True)\n", "\n", "    for base, channel_paths in tqdm(groups.items(), desc=\"Aligning groups\"):\n", "        if 'ch00' not in channel_paths:\n", "            print(f\"[WARNING] Skipping '{base}': no ch00 channel found.\")\n", "            continue\n", "\n", "        print(f\"\\n[INFO] Processing group: {base}\")\n", "        channels = load_channels(channel_paths)\n", "        moving_ch00 = cp.asarray(channels.get('ch00'))\n", "        if moving_ch00 is None:\n", "            print(f\"[WARNING] Could not load 'ch00' for group '{base}'. Skipping.\")\n", "            continue\n", "\n", "        all_imgs = [fixed_ch00] + list(channels.values())\n", "        all_padded = pad_to_largest(all_imgs)\n", "        fixed_ch00_padded = all_padded[0]\n", "        channels_padded = dict(zip(channel_paths.keys(), all_padded[1:]))\n", "\n", "        print(\" - Estimating initial shift using phase correlation...\")\n", "        shift, _, _ = phase_cross_correlation(\n", "            cp.asnumpy(fixed_ch00_padded), cp.asnumpy(channels_padded['ch00']), upsample_factor=5\n", "        )\n", "\n", "        for ch_name in channels_padded:\n", "            current_img = channels_padded[ch_name]\n", "            shifted = cupy_fourier_shift(current_img, shift)\n", "            channels_padded[ch_name] = shifted\n", "            cleanup_memory()\n", "\n", "        best_similarity = -1\n", "        best_img = None\n", "        best_transforms = []\n", "\n", "        print(\" - Running affine registration...\")\n", "        start_time = time.time()\n", "        affine_img, affine_transforms, aff_sim = ants_registration_optimized(\n", "            fixed_ch00_padded,\n", "            channels_padded['ch00'],\n", "            transform_type=affine_transform_type,\n", "            interpolator=reg_interpolator,\n", "            downsample_factor=1\n", "        )\n", "        print(f\"   >> Affine similarity: {aff_sim:.6f} (took {time.time() - start_time:.2f}s)\")\n", "        best_similarity = aff_sim\n", "        best_img = affine_img\n", "        best_transforms = affine_transforms\n", "        cleanup_memory()\n", "\n", "        if use_non_rigid:\n", "            print(\" - Running non-rigid registration...\")\n", "            start_time = time.time()\n", "            nonrigid_img, nonrigid_transforms, nonrigid_sim = ants_registration_optimized(\n", "                fixed_ch00_padded,\n", "                best_img,\n", "                transform_type=\"SyN\",\n", "                interpolator=reg_interpolator,\n", "                downsample_factor=downsample_factor\n", "            )\n", "            print(f\"   >> Non-rigid similarity: {nonrigid_sim:.6f} (took {time.time() - start_time:.2f}s)\")\n", "            if nonrigid_sim > best_similarity:\n", "                best_similarity = nonrigid_sim\n", "                best_img = nonrigid_img\n", "                best_transforms += nonrigid_transforms\n", "            cleanup_memory()\n", "\n", "        if use_micro_registration and best_similarity < similarity_threshold:\n", "            print(\" - Running micro-registration...\")\n", "            start_time = time.time()\n", "            try:\n", "                micro_img, micro_transforms, micro_sim = ants_registration_micro(\n", "                    fixed_ch00_padded,\n", "                    best_img,\n", "                    interpolator=reg_interpolator,\n", "                    downsample_factor=downsample_factor,\n", "                    timeout=60\n", "                )\n", "                elapsed = time.time() - start_time\n", "                print(f\"   >> Micro-registration similarity: {micro_sim:.6f} (took {elapsed:.2f}s)\")\n", "                if micro_sim > best_similarity:\n", "                    best_similarity = micro_sim\n", "                    best_img = micro_img\n", "                    best_transforms += micro_transforms\n", "            except Exception as e:\n", "                print(f\"   >> Micro-registration failed or timed out: {e}\")\n", "            cleanup_memory()\n", "        elif use_micro_registration:\n", "            print(f\" - Skipping micro-registration (similarity {best_similarity:.3f} > threshold {similarity_threshold})\")\n", "\n", "        print(f\"   >> Final similarity: {best_similarity:.6f}\")\n", "\n", "        if show_overlay:\n", "            plot_overlay(fixed_ch00_padded, best_img, title=f\"Overlay: {base}\")\n", "\n", "        for ch_name, img in channels_padded.items():\n", "            if ch_name == 'ch00':\n", "                warped = best_img\n", "            else:\n", "                warped = apply_transforms_to_channel(\n", "                    img, fixed_ch00_padded, best_transforms, interpolator=apply_interpolator\n", "                )\n", "            save_image(warped, output_folder, f\"{base}_{ch_name}.tif\", channels[ch_name])\n", "            cleanup_memory()\n", "\n", "        if save_intermediate:\n", "            np.save(os.path.join(output_folder, f\"{base}_transform_list.npy\"), best_transforms)\n", "\n", "        print(f\"[INFO] Completed processing group: {base}\")\n", "        cleanup_memory()\n"]}, {"cell_type": "code", "execution_count": 19, "metadata": {"vscode": {"languageId": "markdown"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[INFO] Loading reference ch00 image from /mnt/d/Users/<USER>/antho 4i alignment/lb06/R2/LB06 BP lung 4i p21 green p16 red 9apr25.lif - R 2_Merged_ch00.tif...\n", "[INFO] Found 10 groups to align.\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Aligning groups:   0%|          | 0/10 [00:00<?, ?it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "[INFO] Processing group: LB06 BP lung 4i aSMA green CD31 red 15apr25.lif - R 2_Merged\n", " - Estimating initial shift using phase correlation...\n", " - Running affine registration...\n", "   >> Affine similarity: 0.894425 (took 81.09s)\n", " - Running non-rigid registration...\n", "   >> Non-rigid similarity: 0.893857 (took 345.14s)\n", " - Running micro-registration...\n", "   >> Micro-registration failed or timed out: Micro-registration exceeded 60s timeout\n", "   >> Final similarity: 0.894425\n", "Saved image: /mnt/d/Users/<USER>/antho 4i alignment/lb06/R2/aligned_images_pyant/LB06 BP lung 4i aSMA green CD31 red 15apr25.lif - R 2_Merged_ch00.tif\n", "Saved image: /mnt/d/Users/<USER>/antho 4i alignment/lb06/R2/aligned_images_pyant/LB06 BP lung 4i aSMA green CD31 red 15apr25.lif - R 2_Merged_ch01.tif\n", "Saved image: /mnt/d/Users/<USER>/antho 4i alignment/lb06/R2/aligned_images_pyant/LB06 BP lung 4i aSMA green CD31 red 15apr25.lif - R 2_Merged_ch02.tif\n", "[INFO] Completed processing group: LB06 BP lung 4i aSMA green CD31 red 15apr25.lif - R 2_Merged\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Aligning groups:  10%|█         | 1/10 [09:32<1:25:52, 572.54s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "[INFO] Processing group: LB06 BP lung 4i aSMA green didnt work CXCR3 red 14apr25.lif - R 2_Merged\n", " - Estimating initial shift using phase correlation...\n", " - Running affine registration...\n", "   >> Affine similarity: 0.901779 (took 106.02s)\n", " - Running non-rigid registration...\n", "   >> Non-rigid similarity: 0.901146 (took 351.75s)\n", " - Skipping micro-registration (similarity 0.902 > threshold 0.9)\n", "   >> Final similarity: 0.901779\n", "Saved image: /mnt/d/Users/<USER>/antho 4i alignment/lb06/R2/aligned_images_pyant/LB06 BP lung 4i aSMA green didnt work CXCR3 red 14apr25.lif - R 2_Merged_ch00.tif\n", "Saved image: /mnt/d/Users/<USER>/antho 4i alignment/lb06/R2/aligned_images_pyant/LB06 BP lung 4i aSMA green didnt work CXCR3 red 14apr25.lif - R 2_Merged_ch01.tif\n", "Saved image: /mnt/d/Users/<USER>/antho 4i alignment/lb06/R2/aligned_images_pyant/LB06 BP lung 4i aSMA green didnt work CXCR3 red 14apr25.lif - R 2_Merged_ch02.tif\n", "[INFO] Completed processing group: LB06 BP lung 4i aSMA green didnt work CXCR3 red 14apr25.lif - R 2_Merged\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Aligning groups:  20%|██        | 2/10 [18:01<1:11:20, 535.05s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "[INFO] Processing group: LB06 BP lung 4i CD45 green 18apr25.lif - R 2_Merged\n", " - Estimating initial shift using phase correlation...\n", " - Running affine registration...\n", "   >> Affine similarity: 0.497044 (took 72.61s)\n", " - Running non-rigid registration...\n", "   >> Non-rigid similarity: 0.498021 (took 399.94s)\n", " - Running micro-registration...\n", "   >> Micro-registration failed or timed out: Micro-registration exceeded 60s timeout\n", "   >> Final similarity: 0.498021\n", "Saved image: /mnt/d/Users/<USER>/antho 4i alignment/lb06/R2/aligned_images_pyant/LB06 BP lung 4i CD45 green 18apr25.lif - R 2_Merged_ch00.tif\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Aligning groups:  30%|███       | 3/10 [27:50<1:05:17, 559.67s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["Saved image: /mnt/d/Users/<USER>/antho 4i alignment/lb06/R2/aligned_images_pyant/LB06 BP lung 4i CD45 green 18apr25.lif - R 2_Merged_ch01.tif\n", "[INFO] Completed processing group: LB06 BP lung 4i CD45 green 18apr25.lif - R 2_Merged\n", "\n", "[INFO] Processing group: LB06 BP lung 4i HMGB1 green PCNA red 10apr25.lif - R 2_Merged\n", " - Estimating initial shift using phase correlation...\n", " - Running affine registration...\n", "   >> Affine similarity: 0.500911 (took 88.89s)\n", " - Running non-rigid registration...\n", "   >> Non-rigid similarity: 0.500060 (took 413.88s)\n", " - Running micro-registration...\n", "   >> Micro-registration failed or timed out: Micro-registration exceeded 60s timeout\n", "   >> Final similarity: 0.500911\n", "Saved image: /mnt/d/Users/<USER>/antho 4i alignment/lb06/R2/aligned_images_pyant/LB06 BP lung 4i HMGB1 green PCNA red 10apr25.lif - R 2_Merged_ch00.tif\n", "Saved image: /mnt/d/Users/<USER>/antho 4i alignment/lb06/R2/aligned_images_pyant/LB06 BP lung 4i HMGB1 green PCNA red 10apr25.lif - R 2_Merged_ch01.tif\n", "Saved image: /mnt/d/Users/<USER>/antho 4i alignment/lb06/R2/aligned_images_pyant/LB06 BP lung 4i HMGB1 green PCNA red 10apr25.lif - R 2_Merged_ch02.tif\n", "[INFO] Completed processing group: LB06 BP lung 4i HMGB1 green PCNA red 10apr25.lif - R 2_Merged\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Aligning groups:  40%|████      | 4/10 [38:52<1:00:00, 600.12s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "[INFO] Processing group: LB06 BP lung 4i IL1a green IP10 red 16apr25.lif - R 2_Merged\n", " - Estimating initial shift using phase correlation...\n", " - Running affine registration...\n", "   >> Affine similarity: 0.877459 (took 118.64s)\n", " - Running non-rigid registration...\n", "   >> Non-rigid similarity: 0.877193 (took 456.29s)\n", " - Running micro-registration...\n", "   >> Micro-registration failed or timed out: Micro-registration exceeded 60s timeout\n", "   >> Final similarity: 0.877459\n", "Saved image: /mnt/d/Users/<USER>/antho 4i alignment/lb06/R2/aligned_images_pyant/LB06 BP lung 4i IL1a green IP10 red 16apr25.lif - R 2_Merged_ch00.tif\n", "Saved image: /mnt/d/Users/<USER>/antho 4i alignment/lb06/R2/aligned_images_pyant/LB06 BP lung 4i IL1a green IP10 red 16apr25.lif - R 2_Merged_ch01.tif\n", "Saved image: /mnt/d/Users/<USER>/antho 4i alignment/lb06/R2/aligned_images_pyant/LB06 BP lung 4i IL1a green IP10 red 16apr25.lif - R 2_Merged_ch02.tif\n", "[INFO] Completed processing group: LB06 BP lung 4i IL1a green IP10 red 16apr25.lif - R 2_Merged\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Aligning groups:  50%|█████     | 5/10 [50:55<53:42, 644.46s/it]  "]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "[INFO] Processing group: LB06 BP lung 4i LaminB1 green BCL2red 11apr25.lif - R 2_Merged\n", " - Estimating initial shift using phase correlation...\n", " - Running affine registration...\n", "   >> Affine similarity: 0.902590 (took 81.90s)\n", " - Running non-rigid registration...\n", "   >> Non-rigid similarity: 0.902490 (took 341.64s)\n", " - Skipping micro-registration (similarity 0.903 > threshold 0.9)\n", "   >> Final similarity: 0.902590\n", "Saved image: /mnt/d/Users/<USER>/antho 4i alignment/lb06/R2/aligned_images_pyant/LB06 BP lung 4i LaminB1 green BCL2red 11apr25.lif - R 2_Merged_ch00.tif\n", "Saved image: /mnt/d/Users/<USER>/antho 4i alignment/lb06/R2/aligned_images_pyant/LB06 BP lung 4i LaminB1 green BCL2red 11apr25.lif - R 2_Merged_ch01.tif\n", "Saved image: /mnt/d/Users/<USER>/antho 4i alignment/lb06/R2/aligned_images_pyant/LB06 BP lung 4i LaminB1 green BCL2red 11apr25.lif - R 2_Merged_ch02.tif\n", "[INFO] Completed processing group: LB06 BP lung 4i LaminB1 green BCL2red 11apr25.lif - R 2_Merged\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Aligning groups:  60%|██████    | 6/10 [58:44<38:59, 584.80s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "[INFO] Processing group: LB06 BP lung 4i MMP red 19apr25.lif - R 2_Merged\n", " - Estimating initial shift using phase correlation...\n", " - Running affine registration...\n", "   >> Affine similarity: 0.510430 (took 66.29s)\n", " - Running non-rigid registration...\n", "   >> Non-rigid similarity: 0.510592 (took 362.06s)\n", " - Running micro-registration...\n", "   >> Micro-registration failed or timed out: Micro-registration exceeded 60s timeout\n", "   >> Final similarity: 0.510592\n", "Saved image: /mnt/d/Users/<USER>/antho 4i alignment/lb06/R2/aligned_images_pyant/LB06 BP lung 4i MMP red 19apr25.lif - R 2_Merged_ch00.tif\n", "Saved image: /mnt/d/Users/<USER>/antho 4i alignment/lb06/R2/aligned_images_pyant/LB06 BP lung 4i MMP red 19apr25.lif - R 2_Merged_ch01.tif\n", "[INFO] Completed processing group: LB06 BP lung 4i MMP red 19apr25.lif - R 2_Merged\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Aligning groups:  70%|███████   | 7/10 [1:07:53<28:39, 573.01s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "[INFO] Processing group: LB06 BP lung 4i p21 green p16 red 9apr25.lif - R 2_Merged\n", " - Estimating initial shift using phase correlation...\n", " - Running affine registration...\n", "   >> Affine similarity: 1.000000 (took 86.95s)\n", " - Running non-rigid registration...\n", "   >> Non-rigid similarity: 0.999890 (took 344.69s)\n", " - Skipping micro-registration (similarity 1.000 > threshold 0.9)\n", "   >> Final similarity: 1.000000\n", "Saved image: /mnt/d/Users/<USER>/antho 4i alignment/lb06/R2/aligned_images_pyant/LB06 BP lung 4i p21 green p16 red 9apr25.lif - R 2_Merged_ch00.tif\n", "Saved image: /mnt/d/Users/<USER>/antho 4i alignment/lb06/R2/aligned_images_pyant/LB06 BP lung 4i p21 green p16 red 9apr25.lif - R 2_Merged_ch01.tif\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Aligning groups:  80%|████████  | 8/10 [1:15:49<18:04, 542.21s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["Saved image: /mnt/d/Users/<USER>/antho 4i alignment/lb06/R2/aligned_images_pyant/LB06 BP lung 4i p21 green p16 red 9apr25.lif - R 2_Merged_ch02.tif\n", "[INFO] Completed processing group: LB06 BP lung 4i p21 green p16 red 9apr25.lif - R 2_Merged\n", "\n", "[INFO] Processing group: LB06 BP lung 4i PancytoK green vimentin red 17apr25.lif - R 2_Merged\n", " - Estimating initial shift using phase correlation...\n", " - Running affine registration...\n", "   >> Affine similarity: 0.503979 (took 57.04s)\n", " - Running non-rigid registration...\n", "   >> Non-rigid similarity: 0.512382 (took 335.67s)\n", " - Running micro-registration...\n", "   >> Micro-registration failed or timed out: Micro-registration exceeded 60s timeout\n", "   >> Final similarity: 0.512382\n", "Saved image: /mnt/d/Users/<USER>/antho 4i alignment/lb06/R2/aligned_images_pyant/LB06 BP lung 4i PancytoK green vimentin red 17apr25.lif - R 2_Merged_ch00.tif\n", "Saved image: /mnt/d/Users/<USER>/antho 4i alignment/lb06/R2/aligned_images_pyant/LB06 BP lung 4i PancytoK green vimentin red 17apr25.lif - R 2_Merged_ch01.tif\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Aligning groups:  90%|█████████ | 9/10 [1:24:28<08:54, 534.81s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["Saved image: /mnt/d/Users/<USER>/antho 4i alignment/lb06/R2/aligned_images_pyant/LB06 BP lung 4i PancytoK green vimentin red 17apr25.lif - R 2_Merged_ch02.tif\n", "[INFO] Completed processing group: LB06 BP lung 4i PancytoK green vimentin red 17apr25.lif - R 2_Merged\n", "\n", "[INFO] Processing group: LB06 BP lung 4i vimentin red 22apr25.lif - R 2_Merged\n", " - Estimating initial shift using phase correlation...\n", " - Running affine registration...\n", "   >> Affine similarity: 0.900655 (took 108.47s)\n", " - Running non-rigid registration...\n", "   >> Non-rigid similarity: 0.900285 (took 433.24s)\n", " - Skipping micro-registration (similarity 0.901 > threshold 0.9)\n", "   >> Final similarity: 0.900655\n", "Saved image: /mnt/d/Users/<USER>/antho 4i alignment/lb06/R2/aligned_images_pyant/LB06 BP lung 4i vimentin red 22apr25.lif - R 2_Merged_ch00.tif\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Aligning groups: 100%|██████████| 10/10 [1:34:11<00:00, 565.14s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["Saved image: /mnt/d/Users/<USER>/antho 4i alignment/lb06/R2/aligned_images_pyant/LB06 BP lung 4i vimentin red 22apr25.lif - R 2_Merged_ch01.tif\n", "[INFO] Completed processing group: LB06 BP lung 4i vimentin red 22apr25.lif - R 2_Merged\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}], "source": ["if __name__ == \"__main__\":\n", "    PADDED_DIR = \"/mnt/d/Users/<USER>/antho 4i alignment/lb06/R2/\"\n", "    REFERENCE_IMG = (\n", "        \"/mnt/d/Users/<USER>/antho 4i alignment/\"\n", "        \"lb06/R2/LB06 BP lung 4i p21 green p16 red 9apr25.lif - R 2_Merged_ch00.tif\"\n", "    )\n", "    OUTPUT_DIR = os.path.join(PADDED_DIR, \"aligned_images_pyant\")\n", "\n", "    batch_align_to_reference_optimized(\n", "        REFERENCE_IMG,\n", "        PADDED_DIR,\n", "        OUTPUT_DIR,\n", "        affine_transform_type=\"Affine\",\n", "        use_non_rigid=True,\n", "        use_micro_registration=True,\n", "        similarity_threshold=0.90,     # Very high threshold\n", "        downsample_factor=2,\n", "        save_intermediate=False,\n", "    )\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"vscode": {"languageId": "markdown"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[INFO] Loading reference ch00 image from /mnt/d/Users/<USER>/antho 4i alignment/lb06/R2/LB06 BP lung 4i p21 green p16 red 9apr25.lif - R 2_Merged_ch00.tif...\n", "[INFO] Found 10 groups to align.\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Aligning groups:   0%|          | 0/10 [00:00<?, ?it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "[INFO] Processing group: LB06 BP lung 4i aSMA green CD31 red 15apr25.lif - R 2_Merged\n", " - Padding images...\n", " - Estimating initial shift using phase correlation...\n", " - Running affine registration...\n", "   >> Affine similarity: 0.894428 (took 66.57s)\n", " - Running non-rigid registration...\n", "   >> Non-rigid similarity: 0.893848 (took 338.41s)\n", " - Running micro-registration...\n", "   >> Micro-registration failed or timed out: Micro-registration exceeded 60s timeout\n", "   >> Final similarity: 0.894428\n", "Saved image: /mnt/d/Users/<USER>/antho 4i alignment/lb06/R2/aligned_images_pyant/LB06 BP lung 4i aSMA green CD31 red 15apr25.lif - R 2_Merged_ch00.tif\n", "Saved image: /mnt/d/Users/<USER>/antho 4i alignment/lb06/R2/aligned_images_pyant/LB06 BP lung 4i aSMA green CD31 red 15apr25.lif - R 2_Merged_ch01.tif\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Aligning groups:  10%|█         | 1/10 [08:46<1:18:57, 526.39s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["Saved image: /mnt/d/Users/<USER>/antho 4i alignment/lb06/R2/aligned_images_pyant/LB06 BP lung 4i aSMA green CD31 red 15apr25.lif - R 2_Merged_ch02.tif\n", "[INFO] Completed processing group: LB06 BP lung 4i aSMA green CD31 red 15apr25.lif - R 2_Merged\n", "\n", "[INFO] Processing group: LB06 BP lung 4i aSMA green didnt work CXCR3 red 14apr25.lif - R 2_Merged\n", " - Padding images...\n", " - Estimating initial shift using phase correlation...\n", " - Running affine registration...\n", "   >> Affine similarity: 0.901788 (took 64.12s)\n", " - Running non-rigid registration...\n", "   >> Non-rigid similarity: 0.901177 (took 337.16s)\n", " - Skipping micro-registration (similarity 0.902 > threshold 0.9)\n", "   >> Final similarity: 0.901788\n", "Saved image: /mnt/d/Users/<USER>/antho 4i alignment/lb06/R2/aligned_images_pyant/LB06 BP lung 4i aSMA green didnt work CXCR3 red 14apr25.lif - R 2_Merged_ch00.tif\n", "Saved image: /mnt/d/Users/<USER>/antho 4i alignment/lb06/R2/aligned_images_pyant/LB06 BP lung 4i aSMA green didnt work CXCR3 red 14apr25.lif - R 2_Merged_ch01.tif\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Aligning groups:  20%|██        | 2/10 [16:11<1:03:48, 478.57s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["Saved image: /mnt/d/Users/<USER>/antho 4i alignment/lb06/R2/aligned_images_pyant/LB06 BP lung 4i aSMA green didnt work CXCR3 red 14apr25.lif - R 2_Merged_ch02.tif\n", "[INFO] Completed processing group: LB06 BP lung 4i aSMA green didnt work CXCR3 red 14apr25.lif - R 2_Merged\n", "[WARNING] Skipping 'LB06 BP lung 4i CD45 green 18apr25.lif - R 2_Merged': no ch00 channel found.\n", "\n", "[INFO] Processing group: LB06 BP lung 4i HMGB1 green PCNA red 10apr25.lif - R 2_Merged\n", " - Padding images...\n", " - Estimating initial shift using phase correlation...\n", " - Running affine registration...\n", "   >> Affine similarity: 0.500028 (took 52.97s)\n", " - Running non-rigid registration...\n", "   >> Non-rigid similarity: 0.499446 (took 341.78s)\n", " - Running micro-registration...\n", "   >> Micro-registration failed or timed out: Micro-registration exceeded 60s timeout\n", "   >> Final similarity: 0.500028\n", "Saved image: /mnt/d/Users/<USER>/antho 4i alignment/lb06/R2/aligned_images_pyant/LB06 BP lung 4i HMGB1 green PCNA red 10apr25.lif - R 2_Merged_ch00.tif\n", "Saved image: /mnt/d/Users/<USER>/antho 4i alignment/lb06/R2/aligned_images_pyant/LB06 BP lung 4i HMGB1 green PCNA red 10apr25.lif - R 2_Merged_ch01.tif\n", "Saved image: /mnt/d/Users/<USER>/antho 4i alignment/lb06/R2/aligned_images_pyant/LB06 BP lung 4i HMGB1 green PCNA red 10apr25.lif - R 2_Merged_ch02.tif\n", "[INFO] Completed processing group: LB06 BP lung 4i HMGB1 green PCNA red 10apr25.lif - R 2_Merged\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Aligning groups:  40%|████      | 4/10 [24:48<34:03, 340.57s/it]  "]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "[INFO] Processing group: LB06 BP lung 4i IL1a green IP10 red 16apr25.lif - R 2_Merged\n", " - Padding images...\n", " - Estimating initial shift using phase correlation...\n", " - Running affine registration...\n", "   >> Affine similarity: 0.877457 (took 61.82s)\n", " - Running non-rigid registration...\n", "   >> Non-rigid similarity: 0.877190 (took 331.12s)\n", " - Running micro-registration...\n", "   >> Micro-registration failed or timed out: Micro-registration exceeded 60s timeout\n", "   >> Final similarity: 0.877457\n", "Saved image: /mnt/d/Users/<USER>/antho 4i alignment/lb06/R2/aligned_images_pyant/LB06 BP lung 4i IL1a green IP10 red 16apr25.lif - R 2_Merged_ch00.tif\n", "Saved image: /mnt/d/Users/<USER>/antho 4i alignment/lb06/R2/aligned_images_pyant/LB06 BP lung 4i IL1a green IP10 red 16apr25.lif - R 2_Merged_ch01.tif\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Aligning groups:  50%|█████     | 5/10 [33:25<32:55, 395.06s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["Saved image: /mnt/d/Users/<USER>/antho 4i alignment/lb06/R2/aligned_images_pyant/LB06 BP lung 4i IL1a green IP10 red 16apr25.lif - R 2_Merged_ch02.tif\n", "[INFO] Completed processing group: LB06 BP lung 4i IL1a green IP10 red 16apr25.lif - R 2_Merged\n", "\n", "[INFO] Processing group: LB06 BP lung 4i LaminB1 green BCL2red 11apr25.lif - R 2_Merged\n", " - Padding images...\n", " - Estimating initial shift using phase correlation...\n", " - Running affine registration...\n", "   >> Affine similarity: 0.902600 (took 63.97s)\n", " - Running non-rigid registration...\n", "   >> Non-rigid similarity: 0.902434 (took 332.34s)\n", " - Skipping micro-registration (similarity 0.903 > threshold 0.9)\n", "   >> Final similarity: 0.902600\n", "Saved image: /mnt/d/Users/<USER>/antho 4i alignment/lb06/R2/aligned_images_pyant/LB06 BP lung 4i LaminB1 green BCL2red 11apr25.lif - R 2_Merged_ch00.tif\n", "Saved image: /mnt/d/Users/<USER>/antho 4i alignment/lb06/R2/aligned_images_pyant/LB06 BP lung 4i LaminB1 green BCL2red 11apr25.lif - R 2_Merged_ch01.tif\n", "Saved image: /mnt/d/Users/<USER>/antho 4i alignment/lb06/R2/aligned_images_pyant/LB06 BP lung 4i LaminB1 green BCL2red 11apr25.lif - R 2_Merged_ch02.tif\n", "[INFO] Completed processing group: LB06 BP lung 4i LaminB1 green BCL2red 11apr25.lif - R 2_Merged\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Aligning groups:  60%|██████    | 6/10 [40:45<27:15, 409.00s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "[INFO] Processing group: LB06 BP lung 4i MMP red 19apr25.lif - R 2_Merged\n", " - Padding images...\n", " - Estimating initial shift using phase correlation...\n", " - Running affine registration...\n", "   >> Affine similarity: 0.508614 (took 50.76s)\n", " - Running non-rigid registration...\n", "   >> Non-rigid similarity: 0.509194 (took 329.26s)\n", " - Running micro-registration...\n", "   >> Micro-registration failed or timed out: Micro-registration exceeded 60s timeout\n", "   >> Final similarity: 0.509194\n", "Saved image: /mnt/d/Users/<USER>/antho 4i alignment/lb06/R2/aligned_images_pyant/LB06 BP lung 4i MMP red 19apr25.lif - R 2_Merged_ch00.tif\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Aligning groups:  70%|███████   | 7/10 [48:56<21:41, 433.82s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["Saved image: /mnt/d/Users/<USER>/antho 4i alignment/lb06/R2/aligned_images_pyant/LB06 BP lung 4i MMP red 19apr25.lif - R 2_Merged_ch01.tif\n", "[INFO] Completed processing group: LB06 BP lung 4i MMP red 19apr25.lif - R 2_Merged\n", "\n", "[INFO] Processing group: LB06 BP lung 4i p21 green p16 red 9apr25.lif - R 2_Merged\n", " - Padding images...\n", " - Estimating initial shift using phase correlation...\n", " - Running affine registration...\n", "   >> Affine similarity: 1.000000 (took 63.55s)\n", " - Running non-rigid registration...\n", "   >> Non-rigid similarity: 1.000000 (took 334.56s)\n", " - Skipping micro-registration (similarity 1.000 > threshold 0.9)\n", "   >> Final similarity: 1.000000\n", "Saved image: /mnt/d/Users/<USER>/antho 4i alignment/lb06/R2/aligned_images_pyant/LB06 BP lung 4i p21 green p16 red 9apr25.lif - R 2_Merged_ch00.tif\n", "Saved image: /mnt/d/Users/<USER>/antho 4i alignment/lb06/R2/aligned_images_pyant/LB06 BP lung 4i p21 green p16 red 9apr25.lif - R 2_Merged_ch01.tif\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Aligning groups:  80%|████████  | 8/10 [56:16<14:31, 435.73s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["Saved image: /mnt/d/Users/<USER>/antho 4i alignment/lb06/R2/aligned_images_pyant/LB06 BP lung 4i p21 green p16 red 9apr25.lif - R 2_Merged_ch02.tif\n", "[INFO] Completed processing group: LB06 BP lung 4i p21 green p16 red 9apr25.lif - R 2_Merged\n", "\n", "[INFO] Processing group: LB06 BP lung 4i PancytoK green vimentin red 17apr25.lif - R 2_Merged\n", " - Padding images...\n", " - Estimating initial shift using phase correlation...\n", " - Running affine registration...\n", "   >> Affine similarity: 0.504607 (took 49.26s)\n", " - Running non-rigid registration...\n", "   >> Non-rigid similarity: 0.496859 (took 329.87s)\n", " - Running micro-registration...\n", "   >> Micro-registration failed or timed out: Micro-registration exceeded 60s timeout\n", "   >> Final similarity: 0.504607\n", "Saved image: /mnt/d/Users/<USER>/antho 4i alignment/lb06/R2/aligned_images_pyant/LB06 BP lung 4i PancytoK green vimentin red 17apr25.lif - R 2_Merged_ch00.tif\n", "Saved image: /mnt/d/Users/<USER>/antho 4i alignment/lb06/R2/aligned_images_pyant/LB06 BP lung 4i PancytoK green vimentin red 17apr25.lif - R 2_Merged_ch01.tif\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Aligning groups:  90%|█████████ | 9/10 [1:04:30<07:33, 453.44s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["Saved image: /mnt/d/Users/<USER>/antho 4i alignment/lb06/R2/aligned_images_pyant/LB06 BP lung 4i PancytoK green vimentin red 17apr25.lif - R 2_Merged_ch02.tif\n", "[INFO] Completed processing group: LB06 BP lung 4i PancytoK green vimentin red 17apr25.lif - R 2_Merged\n", "\n", "[INFO] Processing group: LB06 BP lung 4i vimentin red 22apr25.lif - R 2_Merged\n", " - Padding images...\n", " - Estimating initial shift using phase correlation...\n", " - Running affine registration...\n", "   >> Affine similarity: 0.900666 (took 65.46s)\n", " - Running non-rigid registration...\n", "   >> Non-rigid similarity: 0.900298 (took 339.48s)\n", " - Skipping micro-registration (similarity 0.901 > threshold 0.9)\n", "   >> Final similarity: 0.900666\n", "Saved image: /mnt/d/Users/<USER>/antho 4i alignment/lb06/R2/aligned_images_pyant/LB06 BP lung 4i vimentin red 22apr25.lif - R 2_Merged_ch00.tif\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Aligning groups: 100%|██████████| 10/10 [1:11:49<00:00, 430.93s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["Saved image: /mnt/d/Users/<USER>/antho 4i alignment/lb06/R2/aligned_images_pyant/LB06 BP lung 4i vimentin red 22apr25.lif - R 2_Merged_ch01.tif\n", "[INFO] Completed processing group: LB06 BP lung 4i vimentin red 22apr25.lif - R 2_Merged\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}], "source": ["if __name__ == \"__main__\":\n", "    PADDED_DIR = \"/mnt/d/Users/<USER>/antho 4i alignment/lb06/R2/\"\n", "    REFERENCE_IMG = (\n", "        \"/mnt/d/Users/<USER>/antho 4i alignment/\"\n", "        \"lb06/R2/LB06 BP lung 4i p21 green p16 red 9apr25.lif - R 2_Merged_ch00.tif\"\n", "    )\n", "    OUTPUT_DIR = os.path.join(PADDED_DIR, \"aligned_images_pyant\")\n", "\n", "    batch_align_to_reference_optimized(\n", "        REFERENCE_IMG,\n", "        PADDED_DIR,\n", "        OUTPUT_DIR,\n", "        affine_transform_type=\"Affine\",\n", "        use_non_rigid=False,\n", "        use_micro_registration=True,\n", "        similarity_threshold=0.90,     # Very high threshold\n", "        downsample_factor=2,\n", "        save_intermediate=False,\n", "    )\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"vscode": {"languageId": "markdown"}}, "outputs": [], "source": ["################ ANTpy###########\n", "import os\n", "import re\n", "from pathlib import Path\n", "from typing import List, Dict, Optional\n", "\n", "import cupy as cp\n", "import numpy as np\n", "import tifffile\n", "import ants\n", "import matplotlib.pyplot as plt\n", "import time\n", "from tqdm import tqdm\n", "from skimage.registration import phase_cross_correlation\n", "from scipy.ndimage import fourier_shift\n", "\n", "def normalize_image(img: cp.ndarray) -> cp.ndarray:\n", "    img_min = img.min()\n", "    img_max = img.max()\n", "    return (img - img_min) / (img_max - img_min + 1e-12)\n", "\n", "\n", "def pad_to_largest(images: List[cp.ndarray]) -> List[cp.ndarray]:\n", "    max_h = max(img.shape[0] for img in images)\n", "    max_w = max(img.shape[1] for img in images)\n", "    padded = []\n", "    for img in images:\n", "        pad_h = max_h - img.shape[0]\n", "        pad_w = max_w - img.shape[1]\n", "        pad_top = pad_h // 2\n", "        pad_bottom = pad_h - pad_top\n", "        pad_left = pad_w // 2\n", "        pad_right = pad_w - pad_left\n", "        img_padded = cp.pad(img, ((pad_top, pad_bottom), (pad_left, pad_right)),\n", "                            mode='constant', constant_values=0)\n", "        padded.append(img_padded)\n", "    return padded\n", "\n", "def phase_correlation_shift(fixed: cp.ndarray, moving: cp.ndarray) -> tuple[int, int]:\n", "    F1 = cp.fft.fft2(fixed)\n", "    F2 = cp.fft.fft2(moving)\n", "    R = F1 * cp.conj(F2)\n", "    R /= cp.abs(R) + 1e-8\n", "    r = cp.fft.ifft2(R)\n", "    max_loc = cp.unravel_index(cp.argmax(cp.abs(r)), r.shape)\n", "    shift_y, shift_x = max_loc\n", "    # Correct for wrap-around\n", "    if shift_y > r.shape[0] // 2:\n", "        shift_y -= r.shape[0]\n", "    if shift_x > r.shape[1] // 2:\n", "        shift_x -= r.shape[1]\n", "    return int(shift_y), int(shift_x)\n", "\n", "def affine_initializer_fixed(fixed, moving):\n", "    \"\"\"\n", "    Create an affine initializer for ANTs registration using multiple fallback methods\n", "    to ensure success regardless of ANTs version or input types.\n", "    \n", "    Args:\n", "        fixed: Fixed image (reference)\n", "        moving: Moving image to align\n", "        \n", "    Returns:\n", "        An ANTs transform object\n", "    \"\"\"\n", "    # Convert to numpy arrays if they're cupy arrays\n", "    fixed_np = cp.asnumpy(fixed) if isinstance(fixed, cp.ndarray) else fixed\n", "    moving_np = cp.asnumpy(moving) if isinstance(moving, cp.ndarray) else moving\n", "    \n", "    # Method 1: Try using ANTs' built-in initializer\n", "    try:\n", "        print(\"Trying ANTs built-in initializer...\")\n", "        fixed_ants = ants.from_numpy(fixed_np)\n", "        moving_ants = ants.from_numpy(moving_np)\n", "        tx = ants.initial_transform(fixed=fixed_ants, moving=moving_ants, type_of_transform='Similarity')\n", "        return tx\n", "    except Exception as e:\n", "        print(f\"ANTs initializer failed: {e}\")\n", "    \n", "    # Method 2: Try creating a simple translation transform\n", "    try:\n", "        print(\"Trying simple translation transform...\")\n", "        # Get the shift using phase correlation\n", "        try:\n", "            shift_y, shift_x, _ = phase_cross_correlation(fixed_np, moving_np, upsample_factor=10)\n", "        except Exception as e:\n", "            print(f\"Phase correlation failed: {e}\")\n", "            shift_y, shift_x = phase_correlation_shift(fixed, moving)\n", "        \n", "        print(f\"Computed shift: y={shift_y}, x={shift_x}\")\n", "        \n", "        # Try using identity transform with translation\n", "        tx = ants.new_ants_transform(dimension=2)\n", "        tx.set_parameters([0, 0, 0, 0, -shift_x, -shift_y])\n", "        return tx\n", "    except Exception as e:\n", "        print(f\"Translation transform failed: {e}\")\n", "    \n", "    # Method 3: Last resort - return identity transform\n", "    print(\"Using identity transform as fallback...\")\n", "    return ants.new_ants_transform(dimension=2)\n", "\n", "\n", "def ants_registration(fixed: cp.ndarray, moving: cp.ndarray,\n", "                      transform_type: str,\n", "                      initializer: Optional[ants.ANTsTransform] = None,\n", "                      interpolator: str = 'linear') -> (cp.<PERSON><PERSON><PERSON>, List[str], float):\n", "    fixed_ants = ants_from_cp(normalize_image(fixed))\n", "    moving_ants = ants_from_cp(normalize_image(moving))\n", "    \n", "    if transform_type == \"SyN\":\n", "        reg = ants.registration(\n", "            fixed=fixed_ants, \n", "            moving=moving_ants, \n", "            type_of_transform=transform_type,\n", "            grad_step=0.1,\n", "            flow_sigma=3,\n", "            total_sigma=0,\n", "            syn_metric='CC',\n", "            syn_sampling=32,\n", "            reg_iterations=(100,70,50,20)\n", "        )\n", "    else:\n", "        reg = ants.registration(\n", "            fixed=fixed_ants,\n", "            moving=moving_ants,\n", "            type_of_transform=transform_type,\n", "            initial_transform=initializer\n", "        )\n", "    fwd_transforms = reg.get('fwdtransforms', [])\n", "    if isinstance(fwd_transforms, str):\n", "        fwd_transforms = [fwd_transforms]\n", "\n", "    warped = ants.apply_transforms(\n", "        fixed=fixed_ants,\n", "        moving=moving_ants,\n", "        transformlist=fwd_transforms,\n", "        interpolator=interpolator\n", "    )\n", "    \n", "    similarity = ants.image_similarity(fixed_ants, ants_from_cp(ants_to_cp(warped)), 'MattesMutualInformation')\n", "    return ants_to_cp(warped), fwd_transforms, similarity\n", "\n", "\n", "def apply_shift(img: cp.ndarray, shift: tuple[int, int]) -> cp.ndarray:\n", "    return cp.roll(cp.roll(img, shift[0], axis=0), shift[1], axis=1)\n", "\n", "def ants_from_cp(img: cp.n<PERSON><PERSON>) -> ants.ANTsImage:\n", "    return ants.from_numpy(cp.asnumpy(img))\n", "\n", "\n", "def ants_to_cp(img: ants.ANTsImage) -> cp.n<PERSON>ray:\n", "    return cp.asarray(img.numpy())\n", "\n", "\n", "\n", "def compute_similarity(fixed: cp.ndarray, moving: cp.ndarray) -> float:\n", "    from skimage.metrics import normalized_mutual_information\n", "    return float(normalized_mutual_information(cp.asnumpy(fixed), cp.asnumpy(moving)))\n", "\n", "\n", "\n", "\n", "\n", "def apply_transforms_to_channel(channel: cp.ndarray, fixed: cp.ndarray,\n", "                              transformlist: List[str], \n", "                              interpolator: str = 'bSpline') -> cp.ndarray:\n", "    \"\"\"Apply transforms with advanced interpolation\"\"\"\n", "    fixed_ants = ants_from_cp(fixed)\n", "    moving_ants = ants_from_cp(channel)\n", "    warped = ants.apply_transforms(\n", "        fixed=fixed_ants,\n", "        moving=moving_ants,\n", "        transformlist=transformlist,\n", "        interpolator=interpolator  # Use bspline for better quality\n", "    )\n", "    return ants_to_cp(warped)\n", "\n", "\n", "def save_image(img: cp.n<PERSON>ray, output_dir: str, filename: str):\n", "    out_path = os.path.join(output_dir, filename)\n", "    np_img = cp.asnumpy(img)\n", "    tifffile.imwrite(out_path, np_img.astype(np.float32))\n", "    print(f\"Saved image: {out_path}\")\n", "\n", "\n", "def plot_overlay(fixed: cp.ndarray, moving: cp.ndar<PERSON>, title: str, alpha=0.5):\n", "    fixed_np = cp.asnumpy(normalize_image(fixed))\n", "    moving_np = cp.asnumpy(normalize_image(moving))\n", "    overlay = np.zeros((*fixed_np.shape, 3), dtype=np.float32)\n", "    overlay[..., 0] = fixed_np\n", "    overlay[..., 1] = moving_np\n", "    plt.figure(figsize=(6, 6))\n", "    plt.imshow(overlay)\n", "    plt.title(title)\n", "    plt.axis('off')\n", "    plt.show()\n", "\n", "\n", "def get_basename_key(filename: str) -> str:\n", "    match = re.match(r\"(.+)_ch\\d{2}\\.tif$\", filename)\n", "    if match:\n", "        return match.group(1)\n", "    raise ValueError(f\"Filename {filename} does not match expected pattern *_chXX.tif\")\n", "\n", "\n", "def group_images_by_basename(folder: str) -> Dict[str, Dict[str, str]]:\n", "    files = [f for f in os.listdir(folder) if f.endswith('.tif')]\n", "    groups = {}\n", "    for f in files:\n", "        base = get_basename_key(f)\n", "        ch_match = re.search(r\"_ch(\\d{2})\\.tif$\", f)\n", "        if not ch_match:\n", "            continue\n", "        ch = f\"ch{ch_match.group(1)}\"\n", "        groups.setdefault(base, {})[ch] = os.path.join(folder, f)\n", "    return groups\n", "\n", "\n", "def load_channels(paths: Dict[str, str]) -> Dict[str, cp.ndarray]:\n", "    channels = {}\n", "    for ch, p in paths.items():\n", "        img = tifffile.imread(p)\n", "        if img.ndim > 2:\n", "            img = img[0]\n", "        channels[ch] = cp.asarray(img)\n", "    return channels\n", "\n", "\n", "def plot_overlay(fixed: cp.ndarray, moving: cp.ndar<PERSON>, title: str, alpha=0.5):\n", "    fixed_np = cp.asnumpy(normalize_image(fixed))\n", "    moving_np = cp.asnumpy(normalize_image(moving))\n", "    overlay = np.zeros((*fixed_np.shape, 3), dtype=np.float32)\n", "    overlay[..., 0] = fixed_np\n", "    overlay[..., 1] = moving_np\n", "    \n", "    plt.figure(figsize=(10, 10))\n", "    plt.imshow(overlay)\n", "    plt.title(title)\n", "    plt.axis('off')\n", "    plt.show()\n", "\n", "def cupy_fourier_shift(img: cp.ndarray, shift: tuple) -> cp.ndarray:\n", "    # Free memory explicitly\n", "    cp.get_default_memory_pool().free_all_blocks()\n", "    \n", "    # Use spatial domain shift instead of Fourier for large images\n", "    shift_y, shift_x = shift\n", "    return cp.roll(cp.roll(img, int(shift_y), axis=0), int(shift_x), axis=1)\n", "\n", "def batch_align_to_reference(\n", "    reference_img_path,\n", "    input_folder,\n", "    output_folder,\n", "    affine_transform_type=\"Affine\",\n", "    use_non_rigid=True,\n", "    use_micro_registration=True,\n", "    save_intermediate: bool = False,\n", "    show_overlay: bool = True,\n", "    reg_interpolator=\"bSpline\",\n", "    apply_interpolator=\"bSpline\"\n", "):\n", "    print(f\"[INFO] Loading reference ch00 image from {reference_img_path}...\")\n", "    fixed_ch00 = cp.asarray(tifffile.imread(reference_img_path))\n", "\n", "    groups = group_images_by_basename(input_folder)\n", "    print(f\"[INFO] Found {len(groups)} groups to align.\")\n", "\n", "    Path(output_folder).mkdir(parents=True, exist_ok=True)\n", "\n", "    for base, channel_paths in tqdm(groups.items(), desc=\"Aligning groups\"):\n", "        if 'ch00' not in channel_paths:\n", "            print(f\"[WARNING] Skipping '{base}': no ch00 channel found.\")\n", "            continue\n", "\n", "        print(f\"\\n[INFO] Processing group: {base}\")\n", "        channels = load_channels(channel_paths)\n", "        moving_ch00 = cp.asarray(channels.get('ch00'))\n", "        if moving_ch00 is None:\n", "            print(f\"[WARNING] Could not load 'ch00' for group '{base}'. Skipping.\")\n", "            continue\n", "\n", "        # Pad all images to same size first\n", "        print(\" - Padding images...\")\n", "        all_imgs = [fixed_ch00] + list(channels.values())\n", "        all_padded = pad_to_largest(all_imgs)\n", "        fixed_ch00_padded = all_padded[0]\n", "        channels_padded = dict(zip(channel_paths.keys(), all_padded[1:]))\n", "\n", "        # Phase correlation shift estimation\n", "        print(\" - Estimating initial shift using phase correlation...\")\n", "        shift, _, _ = phase_cross_correlation(\n", "            cp.asnumpy(fixed_ch00_padded), cp.asnumpy(channels_padded['ch00']), upsample_factor=10\n", "        )\n", "\n", "        # Apply shift to all channels\n", "        for ch_name in channels_padded:\n", "            current_img = channels_padded[ch_name]\n", "            # Use direct spatial shift instead of Fourier transform\n", "            shifted = cupy_fourier_shift(current_img, shift)\n", "            channels_padded[ch_name] = shifted\n", "            # Free memory after each iteration\n", "            cp.get_default_memory_pool().free_all_blocks()\n", "\n", "        best_similarity = -1\n", "        best_img = None\n", "        best_transforms = []\n", "\n", "        # Affine registration\n", "        print(\" - Running affine registration...\")\n", "        affine_img, affine_transforms, aff_sim = ants_registration(\n", "            fixed_ch00_padded,\n", "            channels_padded['ch00'],\n", "            transform_type=affine_transform_type,\n", "            interpolator=reg_interpolator\n", "        )\n", "        print(f\"   >> Affine similarity: {aff_sim:.6f}\")\n", "        best_similarity = aff_sim\n", "        best_img = affine_img\n", "        best_transforms = affine_transforms\n", "\n", "        # Non-rigid registration\n", "        if use_non_rigid:\n", "            print(\" - Running non-rigid registration...\")\n", "            nonrigid_img, nonrigid_transforms, nonrigid_sim = ants_registration(\n", "                fixed_ch00_padded,\n", "                best_img,\n", "                transform_type=\"SyN\",\n", "                interpolator=reg_interpolator\n", "            )\n", "            print(f\"   >> Non-rigid similarity: {nonrigid_sim:.6f}\")\n", "            if nonrigid_sim > best_similarity:\n", "                best_similarity = nonrigid_sim\n", "                best_img = nonrigid_img\n", "                best_transforms += nonrigid_transforms\n", "\n", "        # Micro-registration\n", "        if use_micro_registration:\n", "            print(\" - Running micro-registration...\")\n", "            micro_img, micro_transforms, micro_sim = ants_registration(\n", "                fixed_ch00_padded,\n", "                best_img,\n", "                transform_type=\"SyN\",\n", "                interpolator=reg_interpolator\n", "            )\n", "            print(f\"   >> Micro-registration similarity: {micro_sim:.6f}\")\n", "            if micro_sim > best_similarity:\n", "                best_similarity = micro_sim\n", "                best_img = micro_img\n", "                best_transforms += micro_transforms\n", "\n", "        print(f\"   >> Best similarity: {best_similarity:.6f}\")\n", "\n", "        if show_overlay:\n", "            plot_overlay(fixed_ch00_padded, best_img, title=f\"Overlay: {base}\")\n", "\n", "        # Apply final transforms to all padded channels\n", "        for ch_name, img in channels_padded.items():\n", "            if ch_name == 'ch00':\n", "                warped = best_img\n", "            else:\n", "                warped = apply_transforms_to_channel(\n", "                    img, fixed_ch00_padded, best_transforms, interpolator=apply_interpolator\n", "                )\n", "            save_image(warped, output_folder, f\"{base}_{ch_name}.tif\")\n", "\n", "        if save_intermediate:\n", "            np.save(os.path.join(output_folder, f\"{base}_transform_list.npy\"), best_transforms)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"vscode": {"languageId": "markdown"}}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {"vscode": {"languageId": "markdown"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[INFO] Loading reference ch00 image from /mnt/d/Users/<USER>/antho 4i alignment/lb06/R2/LB06 BP lung 4i p21 green p16 red 9apr25.lif - R 2_Merged_ch00.tif...\n", "[INFO] Found 10 groups to align.\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Aligning groups:   0%|          | 0/10 [00:00<?, ?it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "[INFO] Processing group: LB06 BP lung 4i aSMA green CD31 red 15apr25.lif - R 2_Merged\n", " - Padding images...\n", " - Estimating initial shift using phase correlation...\n", " - Computing affine initializer...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Aligning groups:   0%|          | 0/10 [00:46<?, ?it/s]\n"]}, {"ename": "ValueError", "evalue": "setting an array element with a sequence. The requested array has an inhomogeneous shape after 1 dimensions. The detected shape was (3,) + inhomogeneous part.", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31m<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m                                <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[11], line 9\u001b[0m\n\u001b[1;32m      3\u001b[0m REFERENCE_IMG \u001b[38;5;241m=\u001b[39m (\n\u001b[1;32m      4\u001b[0m     \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m/mnt/d/Users/<USER>/antho 4i alignment/\u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[1;32m      5\u001b[0m     \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mlb06/R2/LB06 BP lung 4i p21 green p16 red 9apr25.lif - R 2_Merged_ch00.tif\u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[1;32m      6\u001b[0m )\n\u001b[1;32m      7\u001b[0m OUTPUT_DIR \u001b[38;5;241m=\u001b[39m os\u001b[38;5;241m.\u001b[39mpath\u001b[38;5;241m.\u001b[39mjoin(PADDED_DIR, \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124maligned_images_pyant\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n\u001b[0;32m----> 9\u001b[0m \u001b[43mbatch_align_to_reference\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m     10\u001b[0m \u001b[43m    \u001b[49m\u001b[43mREFERENCE_IMG\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m     11\u001b[0m \u001b[43m    \u001b[49m\u001b[43mPADDED_DIR\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m     12\u001b[0m \u001b[43m    \u001b[49m\u001b[43mOUTPUT_DIR\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m     13\u001b[0m \u001b[43m    \u001b[49m\u001b[43maffine_transform_type\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mAffine\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\n\u001b[1;32m     14\u001b[0m \u001b[43m    \u001b[49m\u001b[43muse_non_rigid\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43;01mTrue\u001b[39;49;00m\u001b[43m,\u001b[49m\n\u001b[1;32m     15\u001b[0m \u001b[43m    \u001b[49m\u001b[43muse_micro_registration\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43;01mTrue\u001b[39;49;00m\u001b[43m,\u001b[49m\n\u001b[1;32m     16\u001b[0m \u001b[43m    \u001b[49m\u001b[43msave_intermediate\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43;01mFalse\u001b[39;49;00m\u001b[43m,\u001b[49m\n\u001b[1;32m     17\u001b[0m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n", "Cell \u001b[0;32mIn[10], line 283\u001b[0m, in \u001b[0;36mbatch_align_to_reference\u001b[0;34m(reference_img_path, input_folder, output_folder, affine_transform_type, use_non_rigid, use_micro_registration, save_intermediate, show_overlay, reg_interpolator, apply_interpolator)\u001b[0m\n\u001b[1;32m    281\u001b[0m \u001b[38;5;28mprint\u001b[39m(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m - Computing affine initializer...\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n\u001b[1;32m    282\u001b[0m \u001b[38;5;66;03m# Replace line 282:\u001b[39;00m\n\u001b[0;32m--> 283\u001b[0m affine_init \u001b[38;5;241m=\u001b[39m \u001b[43maffine_initializer_fixed\u001b[49m\u001b[43m(\u001b[49m\u001b[43mfixed_ch00_padded\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mchannels_padded\u001b[49m\u001b[43m[\u001b[49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[38;5;124;43mch00\u001b[39;49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[43m]\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    285\u001b[0m best_similarity \u001b[38;5;241m=\u001b[39m \u001b[38;5;241m-\u001b[39m\u001b[38;5;241m1\u001b[39m\n\u001b[1;32m    286\u001b[0m best_img \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mNone\u001b[39;00m\n", "Cell \u001b[0;32mIn[10], line 74\u001b[0m, in \u001b[0;36maffine_initializer_fixed\u001b[0;34m(fixed, moving)\u001b[0m\n\u001b[1;32m     71\u001b[0m translation \u001b[38;5;241m=\u001b[39m ants\u001b[38;5;241m.\u001b[39mnew_ants_transform(dimension\u001b[38;5;241m=\u001b[39m\u001b[38;5;241m2\u001b[39m, transform_type\u001b[38;5;241m=\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mEuler2DTransform\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n\u001b[1;32m     73\u001b[0m \u001b[38;5;66;03m# Set parameters: x translation, y translation, rotation (in radians)\u001b[39;00m\n\u001b[0;32m---> 74\u001b[0m \u001b[43mtranslation\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mset_parameters\u001b[49m\u001b[43m(\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;241;43m-\u001b[39;49m\u001b[43mshift_x\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m-\u001b[39;49m\u001b[43mshift_y\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m0.0\u001b[39;49m\u001b[43m)\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m     76\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m translation\n", "File \u001b[0;32m~/anaconda3/envs/VALIS2/lib/python3.10/site-packages/ants/core/ants_transform.py:62\u001b[0m, in \u001b[0;36mANTsTransform.set_parameters\u001b[0;34m(self, parameters)\u001b[0m\n\u001b[1;32m     60\u001b[0m \u001b[38;5;250m\u001b[39m\u001b[38;5;124;03m\"\"\" Set parameters of transform \"\"\"\u001b[39;00m\n\u001b[1;32m     61\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(parameters, np\u001b[38;5;241m.\u001b[39mndarray):\n\u001b[0;32m---> 62\u001b[0m     parameters \u001b[38;5;241m=\u001b[39m \u001b[43mnp\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43masarray\u001b[49m\u001b[43m(\u001b[49m\u001b[43mparameters\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m     64\u001b[0m \u001b[38;5;66;03m# if in two dimensions, flatten with fortran ordering\u001b[39;00m\n\u001b[1;32m     65\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m parameters\u001b[38;5;241m.\u001b[39mndim \u001b[38;5;241m>\u001b[39m \u001b[38;5;241m1\u001b[39m:\n", "\u001b[0;31mValueError\u001b[0m: setting an array element with a sequence. The requested array has an inhomogeneous shape after 1 dimensions. The detected shape was (3,) + inhomogeneous part."]}], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import os\n", "import tifffile\n", "import numpy as np\n", "import cupy as cp\n", "import ants\n", "import matplotlib.pyplot as plt\n", "from skimage.transform import resize, rescale\n", "from skimage.color import rgb2hed, hed2rgb\n", "from tqdm import tqdm\n", "\n", "def check_gpu():\n", "    \"\"\"Check if GPU is available and print the number of GPUs detected.\"\"\"\n", "    num_gpus = cp.cuda.runtime.getDeviceCount()\n", "    if num_gpus > 0:\n", "        print(f\"GPUs detected: {num_gpus}\")\n", "    else:\n", "        print(\"No GPUs detected. Using CPU.\")\n", "    return num_gpus > 0\n", "\n", "def get_pixel_size(tif_path):\n", "    \"\"\"Extract pixel size from TIFF metadata.\"\"\"\n", "    with tifffile.TiffFile(tif_path) as tif:\n", "        metadata = tif.pages[0].tags\n", "        if \"XResolution\" in metadata and \"YResolution\" in metadata:\n", "            x_res = metadata[\"XResolution\"].value\n", "            y_res = metadata[\"YResolution\"].value\n", "            return (x_res[1] / x_res[0], y_res[1] / y_res[0])\n", "    return None\n", "\n", "def normalize_image(image, min_val=0, max_val=1):\n", "    \"\"\"Normalize image to [min_val, max_val] range for consistent processing.\"\"\"\n", "    image_cp = cp.asarray(image)\n", "    norm_image = (image_cp - cp.min(image_cp)) / (cp.max(image_cp) - cp.min(image_cp) + 1e-8)\n", "    norm_image = norm_image * (max_val - min_val) + min_val\n", "    return norm_image\n", "\n", "def resize_image(image, scale_y, scale_x, target_shape):\n", "    \"\"\"Resize image while preserving aspect ratio.\"\"\"\n", "    print(f\"Resizing image from {image.shape} with scales (y: {scale_y}, x: {scale_x}) to target shape {target_shape}\")\n", "    image_cp = cp.asarray(image)\n", "    scaled_image = rescale(cp.asnumpy(image_cp), (scale_y, scale_x), anti_aliasing=True, preserve_range=True, channel_axis=-1)\n", "    resized_image = resize(scaled_image, target_shape, anti_aliasing=True, preserve_range=True, channel_axis=-1)\n", "    print(f\"Resized image shape: {resized_image.shape}\")\n", "    return cp.asarray(resized_image)\n", "\n", "def affine_registration(fixed, moving, transform_type=\"Affine\"):\n", "    \"\"\"Perform affine registration and return transformed image and transformation paths.\"\"\"\n", "    fixed_ants = ants.from_numpy(cp.asnumpy(fixed))\n", "    moving_ants = ants.from_numpy(cp.asnumpy(moving))\n", "    reg = ants.registration(fixed_ants, moving_ants, type_of_transform=transform_type)\n", "    affine_transform_paths = reg['fwdtransforms']  # This is a list of file paths\n", "    print(f\"Affine registration completed. Transform paths: {affine_transform_paths}\")\n", "    return cp.asarray(reg['warpedmovout'].numpy()), affine_transform_paths  # Return image + paths\n", "\n", "def non_rigid_registration(fixed, moving):\n", "    \"\"\"Perform non-rigid registration using SyN and return transformed image and transformation paths.\"\"\"\n", "    fixed_ants = ants.from_numpy(cp.asnumpy(fixed))\n", "    moving_ants = ants.from_numpy(cp.asnumpy(moving))\n", "    reg = ants.registration(fixed_ants, moving_ants, type_of_transform=\"SyN\")\n", "    non_rigid_transform_paths = reg['fwdtransforms']  # This is a list of file paths\n", "    print(f\"Non-rigid registration completed. Transform paths: {non_rigid_transform_paths}\")\n", "    return cp.asarray(reg['warpedmovout'].numpy()), non_rigid_transform_paths  # Return image + paths\n", "\n", "def micro_registration(fixed, moving, patch_size=64, overlap=32):\n", "    \"\"\"Perform micro-registration using small patches.\"\"\"\n", "    fixed_ants = ants.from_numpy(cp.asnumpy(fixed))\n", "    moving_ants = ants.from_numpy(cp.asnumpy(moving))\n", "    reg = ants.registration(\n", "        fixed_ants, \n", "        moving_ants, \n", "        type_of_transform=\"SyN\", \n", "        reg_iterations=(20, 10, 0), \n", "        syn_metric='CC', \n", "        syn_sampling=2, \n", "        syn_radius=4, \n", "        syn_patch_size=patch_size, \n", "        syn_overlap=overlap\n", "    )\n", "    micro_transform_paths = reg['fwdtransforms']  # This is a list of file paths\n", "    print(f\"Micro-registration completed. Transform paths: {micro_transform_paths}\")\n", "    return cp.asarray(reg['warpedmovout'].numpy()), micro_transform_paths  # Return image + paths\n", "\n", "def apply_transform(image, transform_paths):\n", "    \"\"\"Apply stored transformations to another channel.\"\"\"\n", "    ants_image = ants.from_numpy(cp.asnumpy(image))\n", "    transformed_image = ants.apply_transforms(fixed=ants_image, moving=ants_image, transformlist=transform_paths)\n", "    image_transformed = cp.asarray(transformed_image.numpy())\n", "    print(f\"Applied transform. Final image shape: {image_transformed.shape}\")\n", "    return image_transformed\n", "\n", "def plot_images(reference, aligned, title):\n", "    \"\"\"Plot reference, aligned, and difference images.\"\"\"\n", "    difference = cp.abs(reference - aligned)\n", "    fig, axes = plt.subplots(1, 3, figsize=(15, 5))\n", "    axes[0].imshow(cp.asnumpy(reference), cmap=\"gray\")\n", "    axes[0].set_title(\"Reference Image\")\n", "    axes[1].imshow(cp.asnumpy(aligned), cmap=\"gray\")\n", "    axes[1].set_title(\"Aligned Image\")\n", "    axes[2].imshow(cp.asnumpy(difference), cmap=\"hot\")\n", "    axes[2].set_title(\"Difference\")\n", "    plt.suptitle(title)\n", "    plt.show()\n", "\n", "def save_image(image, output_directory, filename):\n", "    \"\"\"Save image to disk.\"\"\"\n", "    output_path = os.path.join(output_directory, filename)\n", "    tifffile.imwrite(output_path, cp.asnumpy(image).astype(np.float32))\n", "    print(f\"Saved image to: {output_path}\")\n", "\n", "def align_images(image_sets, reference_image, reference_pixel_size, output_directory, use_non_rigid=True, use_micro_registration=False, affine_transform_type=\"Affine\", normalize=True, save_intermediate=False):\n", "    \"\"\"Align all channels to the reference image.\"\"\"\n", "    os.makedirs(output_directory, exist_ok=True)\n", "    aligned_images = {\"reference\": reference_image}\n", "    transforms = {}\n", "\n", "    if normalize:\n", "        reference_image = normalize_image(reference_image)  # Ensure consistent intensity\n", "    \n", "    for base_name, channels in tqdm(image_sets.items(), desc=\"Processing images\"):\n", "        print(f\"Processing base name: {base_name}\")\n", "        ch00_path = channels['ch00']\n", "        print(f\"Processing {ch00_path}\")\n", "        image = tifffile.imread(ch00_path)\n", "        image_cp = cp.asarray(image)\n", "        \n", "        # Perform color deconvolution to separate channels\n", "        hed_image = rgb2hed(cp.asnumpy(image_cp))\n", "        blue_channel = cp.asarray(hed_image[..., 2])  # Use the blue channel for alignment\n", "\n", "        if normalize:\n", "            blue_channel = normalize_image(blue_channel)  # Normalize intensity before processing\n", "\n", "        image_pixel_size = get_pixel_size(ch00_path)\n", "        if not image_pixel_size:\n", "            print(f\"Skipping {ch00_path}: Pixel size not found.\")\n", "            continue\n", "\n", "        print(f\"Image pixel size: {image_pixel_size}, Reference pixel size: {reference_pixel_size}\")\n", "\n", "        # Rescale image to match reference pixel size\n", "        scale_x, scale_y = image_pixel_size[0] / reference_pixel_size[0], image_pixel_size[1] / reference_pixel_size[1]\n", "        blue_resized = resize_image(blue_channel, scale_y, scale_x, reference_image.shape)\n", "\n", "        if save_intermediate:\n", "            # Save resized image\n", "            save_image(blue_resized, output_directory, f\"{os.path.basename(ch00_path)}_resized.tif\")\n", "\n", "        # Plot resized image\n", "        plt.figure()\n", "        plt.imshow(cp.asnumpy(blue_resized), cmap=\"gray\")\n", "        plt.title(f\"Resized Image: {os.path.basename(ch00_path)}\")\n", "        plt.show()\n", "\n", "        # Apply affine registration\n", "        blue_affine, affine_transform = affine_registration(reference_image, blue_resized, transform_type=affine_transform_type)\n", "\n", "        if save_intermediate:\n", "            # Save affine registered image\n", "            save_image(blue_affine, output_directory, f\"{os.path.basename(ch00_path)}_affine.tif\")\n", "\n", "        # Plot affine registered image\n", "        plt.figure()\n", "        plt.imshow(cp.asnumpy(blue_affine), cmap=\"gray\")\n", "        plt.title(f\"Affine Registered Image: {os.path.basename(ch00_path)}\")\n", "        plt.show()\n", "\n", "        if use_non_rigid:\n", "            # Apply non-rigid registration\n", "            blue_non_rigid, non_rigid_transform = non_rigid_registration(reference_image, blue_affine)\n", "\n", "            if save_intermediate:\n", "                # Save non-rigid registered image\n", "                save_image(blue_non_rigid, output_directory, f\"{os.path.basename(ch00_path)}_non_rigid.tif\")\n", "\n", "            # Plot non-rigid registered image\n", "            plt.figure()\n", "            plt.imshow(cp.asnumpy(blue_non_rigid), cmap=\"gray\")\n", "            plt.title(f\"Non-Rigid Registered Image: {os.path.basename(ch00_path)}\")\n", "            plt.show()\n", "\n", "            # Save transformations\n", "            transforms[ch00_path] = affine_transform + non_rigid_transform\n", "\n", "            plot_images(reference_image, blue_non_rigid, f\"Aligned: {os.path.basename(ch00_path)}\")\n", "            aligned_images[ch00_path] = blue_non_rigid\n", "        else:\n", "            # Save transformations\n", "            transforms[ch00_path] = affine_transform\n", "\n", "            plot_images(reference_image, blue_affine, f\"Aligned: {os.path.basename(ch00_path)}\")\n", "            aligned_images[ch00_path] = blue_affine\n", "\n", "        if use_micro_registration:\n", "            # Apply micro-registration\n", "            blue_micro, micro_transform = micro_registration(reference_image, aligned_images[ch00_path])\n", "\n", "            if save_intermediate:\n", "                # Save micro-registered image\n", "                save_image(blue_micro, output_directory, f\"{os.path.basename(ch00_path)}_micro.tif\")\n", "\n", "            # Plot micro-registered image\n", "            plt.figure()\n", "            plt.imshow(cp.asnumpy(blue_micro), cmap=\"gray\")\n", "            plt.title(f\"Micro-Registered Image: {os.path.basename(ch00_path)}\")\n", "            plt.show()\n", "\n", "            # Save transformations\n", "            transforms[ch00_path] += micro_transform\n", "\n", "            plot_images(reference_image, blue_micro, f\"Aligned: {os.path.basename(ch00_path)}\")\n", "            aligned_images[ch00_path] = blue_micro\n", "\n", "        # Apply the same transformations to the red and green channels\n", "        red_channel = cp.asarray(hed_image[..., 0])\n", "        green_channel = cp.asarray(hed_image[..., 1])\n", "\n", "        red_aligned = apply_transform(red_channel, transforms[ch00_path])\n", "        green_aligned = apply_transform(green_channel, transforms[ch00_path])\n", "\n", "        # Combine the aligned channels back into an RGB image\n", "        aligned_rgb = hed2rgb(np.stack([cp.asnumpy(red_aligned), cp.asnumpy(green_aligned), cp.asnumpy(aligned_images[ch00_path])], axis=-1))\n", "\n", "        # Save the aligned RGB image\n", "        save_image(cp.asarray(aligned_rgb), output_directory, f\"{os.path.basename(ch00_path)}_aligned_rgb.tif\")\n", "\n", "    try:\n", "        # Ensure the reference image is included as the first channel in the stack\n", "        aligned_images = {\"reference\": reference_image, **aligned_images}\n", "\n", "        # Save all aligned images in a multi-channel stack\n", "        output_filename = os.path.join(output_directory, \"aligned_stack.tif\")\n", "        tifffile.imwrite(output_filename, cp.asnumpy(cp.stack(list(aligned_images.values()), axis=0)).astype(np.float32))\n", "        print(f\"Saved aligned images to: {output_filename}\")\n", "    except ValueError as e:\n", "        print(f\"Error stacking images: {e}. Saving images individually.\")\n", "        # Save each aligned image individually\n", "        for channel_id, aligned_image in aligned_images.items():\n", "            save_image(aligned_image, output_directory, f\"{channel_id}_aligned.tif\")\n", "\n", "# Main Execution\n", "if __name__ == \"__main__\":\n", "    if check_gpu():\n", "        src_dir = \"/mnt/d/Users/<USER>/antho 4i alignment/skin dapi hne/\"\n", "        dst_dir = \"/mnt/d/Users/<USER>/antho 4i alignment/skin dapi hne/aligned_images_cupy\"\n", "        reference_slide = \"/mnt/d/Users/<USER>/antho 4i alignment/skin dapi hne/dr3216481 82 male strip 4i_R 1_Merged_ch00.tif\"\n", "\n", "        reference_image = cp.asarray(tifffile.imread(reference_slide))\n", "        reference_pixel_size = get_pixel_size(reference_slide)\n", "\n", "        # Get all image files and group them by base name\n", "        image_sets = {}\n", "        for f in sorted(os.listdir(src_dir)):\n", "            if f.endswith(\".tif\"):\n", "                base_name, channel = f.rsplit('_ch', 1)\n", "                channel = 'ch' + channel.split('.')[0]\n", "                if base_name not in image_sets:\n", "                    image_sets[base_name] = {}\n", "                image_sets[base_name][channel] = os.path.join(src_dir, f)\n", "\n", "        # Ensure the reference image is excluded from the image sets\n", "        image_sets.pop(os.path.basename(reference_slide).rsplit('_ch', 1)[0], None)\n", "\n", "        # Parameters for alignment\n", "        use_non_rigid = True\n", "        use_micro_registration = True\n", "        affine_transform_type = \"Affine\"\n", "        normalize = False\n", "        save_intermediate = True\n", "\n", "        align_images(image_sets, reference_image, reference_pixel_size, dst_dir, use_non_rigid, use_micro_registration, affine_transform_type, normalize, save_intermediate)\n", "    else:\n", "        print(\"No GPU available. Exiting.\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {"vscode": {"languageId": "markdown"}}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import os\n", "import tifffile\n", "import numpy as np\n", "import ants\n", "import matplotlib.pyplot as plt\n", "from skimage.transform import resize, rescale\n", "\n", "def get_pixel_size(tif_path):\n", "    \"\"\"Extract pixel size from TIFF metadata.\"\"\"\n", "    with tifffile.TiffFile(tif_path) as tif:\n", "        metadata = tif.pages[0].tags\n", "        if \"XResolution\" in metadata and \"YResolution\" in metadata:\n", "            x_res = metadata[\"XResolution\"].value\n", "            y_res = metadata[\"YResolution\"].value\n", "            return (x_res[1] / x_res[0], y_res[1] / y_res[0])\n", "    return None\n", "\n", "def normalize_image(image, min_val=0, max_val=1):\n", "    \"\"\"Normalize image to [min_val, max_val] range for consistent processing.\"\"\"\n", "    norm_image = (image - np.min(image)) / (np.max(image) - np.min(image) + 1e-8)\n", "    return norm_image * (max_val - min_val) + min_val\n", "\n", "def rgb_to_grayscale(image):\n", "    \"\"\"Convert an RGB image to grayscale.\"\"\"\n", "    if image.ndim == 3 and image.shape[2] == 3:\n", "        grayscale_image = 0.2989 * image[..., 0] + 0.5870 * image[..., 1] + 0.1140 * image[..., 2]\n", "        return grayscale_image\n", "    return image\n", "\n", "def resize_image(image, scale_y, scale_x, target_shape):\n", "    \"\"\"Resize image while preserving aspect ratio.\"\"\"\n", "    print(f\"Resizing image from {image.shape} with scales (y: {scale_y}, x: {scale_x}) to target shape {target_shape}\")\n", "    scaled_image = rescale(image, (scale_y, scale_x), anti_aliasing=True, preserve_range=True)\n", "    resized_image = resize(scaled_image, target_shape, anti_aliasing=True, preserve_range=True)\n", "    print(f\"Resized image shape: {resized_image.shape}\")\n", "    return resized_image\n", "\n", "def affine_registration(fixed, moving, transform_type=\"Affine\"):\n", "    \"\"\"Perform affine registration and return transformed image and transformation paths.\"\"\"\n", "    fixed_ants = ants.from_numpy(fixed)\n", "    moving_ants = ants.from_numpy(moving)\n", "    reg = ants.registration(fixed_ants, moving_ants, type_of_transform=transform_type)\n", "    affine_transform_paths = reg['fwdtransforms']  # This is a list of file paths\n", "    print(f\"Affine registration completed. Transform paths: {affine_transform_paths}\")\n", "    return reg['warpedmovout'].numpy(), affine_transform_paths  # Return image + paths\n", "\n", "def non_rigid_registration(fixed, moving):\n", "    \"\"\"Perform non-rigid registration using SyN and return transformed image and transformation paths.\"\"\"\n", "    fixed_ants = ants.from_numpy(fixed)\n", "    moving_ants = ants.from_numpy(moving)\n", "    reg = ants.registration(fixed_ants, moving_ants, type_of_transform=\"SyN\")\n", "    non_rigid_transform_paths = reg['fwdtransforms']  # This is a list of file paths\n", "    print(f\"Non-rigid registration completed. Transform paths: {non_rigid_transform_paths}\")\n", "    return reg['warpedmovout'].numpy(), non_rigid_transform_paths  # Return image + paths\n", "\n", "def micro_registration(fixed, moving, patch_size=64, overlap=32):\n", "    \"\"\"Perform micro-registration using small patches.\"\"\"\n", "    fixed_ants = ants.from_numpy(fixed)\n", "    moving_ants = ants.from_numpy(moving)\n", "    reg = ants.registration(\n", "        fixed_ants, \n", "        moving_ants, \n", "        type_of_transform=\"SyN\", \n", "        reg_iterations=(20, 10, 0), \n", "        syn_metric='CC', \n", "        syn_sampling=2, \n", "        syn_radius=4, \n", "        syn_patch_size=patch_size, \n", "        syn_overlap=overlap\n", "    )\n", "    micro_transform_paths = reg['fwdtransforms']  # This is a list of file paths\n", "    print(f\"Micro-registration completed. Transform paths: {micro_transform_paths}\")\n", "    return reg['warpedmovout'].numpy(), micro_transform_paths  # Return image + paths\n", "\n", "def apply_transform(image, transform_paths):\n", "    \"\"\"Apply stored transformations to another channel.\"\"\"\n", "    ants_image = ants.from_numpy(image)\n", "    transformed_image = ants.apply_transforms(fixed=ants_image, moving=ants_image, transformlist=transform_paths)\n", "    image_transformed = transformed_image.numpy()\n", "    print(f\"Applied transform. Final image shape: {image_transformed.shape}\")\n", "    return image_transformed\n", "\n", "def plot_images(reference, aligned, title):\n", "    \"\"\"Plot reference, aligned, and difference images.\"\"\"\n", "    difference = np.abs(reference - aligned)\n", "    fig, axes = plt.subplots(1, 3, figsize=(15, 5))\n", "    axes[0].imshow(reference, cmap=\"gray\")\n", "    axes[0].set_title(\"Reference Image\")\n", "    axes[1].imshow(aligned, cmap=\"gray\")\n", "    axes[1].set_title(\"Aligned Image\")\n", "    axes[2].imshow(difference, cmap=\"hot\")\n", "    axes[2].set_title(\"Difference\")\n", "    plt.suptitle(title)\n", "    plt.show()\n", "\n", "def save_image(image, output_directory, filename):\n", "    \"\"\"Save image to disk.\"\"\"\n", "    output_path = os.path.join(output_directory, filename)\n", "    tifffile.imwrite(output_path, image.astype(np.float32))\n", "    print(f\"Saved image to: {output_path}\")\n", "\n", "def align_images(image_sets, reference_image, reference_pixel_size, output_directory, use_non_rigid=True, use_micro_registration=False, affine_transform_type=\"Affine\", normalize=True, save_intermediate=False):\n", "    \"\"\"Align all channels to the reference image.\"\"\"\n", "    os.makedirs(output_directory, exist_ok=True)\n", "    aligned_images = {\"reference\": reference_image}\n", "    transforms = {}\n", "\n", "    if normalize:\n", "        reference_image = normalize_image(reference_image)  # Ensure consistent intensity\n", "    \n", "    for base_name, channels in image_sets.items():\n", "        print(f\"Processing base name: {base_name}\")\n", "        ch00_path = channels['ch00']\n", "        print(f\"Processing {ch00_path}\")\n", "        image = tifffile.imread(ch00_path)\n", "        grayscale_image = rgb_to_grayscale(image)  # Convert RGB to grayscale for alignment\n", "        if normalize:\n", "            grayscale_image = normalize_image(grayscale_image)  # Normalize intensity before processing\n", "\n", "        image_pixel_size = get_pixel_size(ch00_path)\n", "        if not image_pixel_size:\n", "            print(f\"Skipping {ch00_path}: Pixel size not found.\")\n", "            continue\n", "\n", "        print(f\"Image pixel size: {image_pixel_size}, Reference pixel size: {reference_pixel_size}\")\n", "\n", "        # Rescale image to match reference pixel size\n", "        scale_x, scale_y = image_pixel_size[0] / reference_pixel_size[0], image_pixel_size[1] / reference_pixel_size[1]\n", "        image_resized = resize_image(grayscale_image, scale_y, scale_x, reference_image.shape)\n", "\n", "        if save_intermediate:\n", "            # Save resized image\n", "            save_image(image_resized, output_directory, f\"{os.path.basename(ch00_path)}_resized.tif\")\n", "\n", "        # Plot resized image\n", "        plt.figure()\n", "        plt.imshow(image_resized, cmap=\"gray\")\n", "        plt.title(f\"Resized Image: {os.path.basename(ch00_path)}\")\n", "        plt.show()\n", "\n", "        # Apply affine registration\n", "        image_affine, affine_transform = affine_registration(reference_image, image_resized, transform_type=affine_transform_type)\n", "\n", "        if save_intermediate:\n", "            # Save affine registered image\n", "            save_image(image_affine, output_directory, f\"{os.path.basename(ch00_path)}_affine.tif\")\n", "\n", "        # Plot affine registered image\n", "        plt.figure()\n", "        plt.imshow(image_affine, cmap=\"gray\")\n", "        plt.title(f\"Affine Registered Image: {os.path.basename(ch00_path)}\")\n", "        plt.show()\n", "\n", "        if use_non_rigid:\n", "            # Apply non-rigid registration\n", "            image_non_rigid, non_rigid_transform = non_rigid_registration(reference_image, image_affine)\n", "\n", "            if save_intermediate:\n", "                # Save non-rigid registered image\n", "                save_image(image_non_rigid, output_directory, f\"{os.path.basename(ch00_path)}_non_rigid.tif\")\n", "\n", "            # Plot non-rigid registered image\n", "            plt.figure()\n", "            plt.imshow(image_non_rigid, cmap=\"gray\")\n", "            plt.title(f\"Non-Rigid Registered Image: {os.path.basename(ch00_path)}\")\n", "            plt.show()\n", "\n", "            # Save transformations\n", "            transforms[ch00_path] = affine_transform + non_rigid_transform\n", "\n", "            plot_images(reference_image, image_non_rigid, f\"Aligned: {os.path.basename(ch00_path)}\")\n", "            aligned_images[ch00_path] = image_non_rigid\n", "        else:\n", "            # Save transformations\n", "            transforms[ch00_path] = affine_transform\n", "\n", "            plot_images(reference_image, image_affine, f\"Aligned: {os.path.basename(ch00_path)}\")\n", "            aligned_images[ch00_path] = image_affine\n", "\n", "        if use_micro_registration:\n", "            # Apply micro-registration\n", "            image_micro, micro_transform = micro_registration(reference_image, aligned_images[ch00_path])\n", "\n", "            if save_intermediate:\n", "                # Save micro-registered image\n", "                save_image(image_micro, output_directory, f\"{os.path.basename(ch00_path)}_micro.tif\")\n", "\n", "            # Plot micro-registered image\n", "            plt.figure()\n", "            plt.imshow(image_micro, cmap=\"gray\")\n", "            plt.title(f\"Micro-Registered Image: {os.path.basename(ch00_path)}\")\n", "            plt.show()\n", "\n", "            # Save transformations\n", "            transforms[ch00_path] += micro_transform\n", "\n", "            plot_images(reference_image, image_micro, f\"Aligned: {os.path.basename(ch00_path)}\")\n", "            aligned_images[ch00_path] = image_micro\n", "\n", "        # Align other channels using the same transformations as ch00\n", "        for channel_id, ch_path in channels.items():\n", "            if channel_id == 'ch00':\n", "                continue\n", "            print(f\"Processing {ch_path}\")\n", "            ch_image = tifffile.imread(ch_path)\n", "            grayscale_ch_image = rgb_to_grayscale(ch_image)  # Convert RGB to grayscale for alignment\n", "            if normalize:\n", "                grayscale_ch_image = normalize_image(grayscale_ch_image)\n", "\n", "            # Rescale ch image to match reference pixel size\n", "            ch_resized = resize_image(grayscale_ch_image, scale_y, scale_x, reference_image.shape)\n", "\n", "            transform_paths = transforms[ch00_path]\n", "            ch_aligned = apply_transform(ch_resized, transform_paths)\n", "\n", "            plot_images(reference_image, ch_aligned, f\"Aligned {channel_id}: {os.path.basename(ch_path)}\")\n", "            aligned_images[ch_path] = ch_aligned\n", "\n", "    try:\n", "        # Ensure the reference image is included as the first channel in the stack\n", "        aligned_images = {\"reference\": reference_image, **aligned_images}\n", "\n", "        # Save all aligned images in a multi-channel stack\n", "        output_filename = os.path.join(output_directory, \"aligned_stack.tif\")\n", "        tifffile.imwrite(output_filename, np.stack(list(aligned_images.values()), axis=0).astype(np.float32))\n", "        print(f\"Saved aligned images to: {output_filename}\")\n", "    except ValueError as e:\n", "        print(f\"Error stacking images: {e}. Saving images individually.\")\n", "        # Save each aligned image individually\n", "        for channel_id, aligned_image in aligned_images.items():\n", "            save_image(aligned_image, output_directory, f\"{channel_id}_aligned.tif\")\n", "\n", "# Main Execution\n", "src_dir = \"/mnt/d/Users/<USER>/antho 4i alignment/skin dapi hne/\"\n", "dst_dir = \"/mnt/d/Users/<USER>/antho 4i alignment/skin dapi hne/aligned_images\"\n", "reference_slide = \"/mnt/d/Users/<USER>/antho 4i alignment/skin dapi hne/dr3216481 82 male strip 4i_R 1_Merged_ch00.tif\"\n", "\n", "reference_image = tifffile.imread(reference_slide)\n", "reference_pixel_size = get_pixel_size(reference_slide)\n", "\n", "# Get all image files and group them by base name\n", "image_sets = {}\n", "for f in sorted(os.listdir(src_dir)):\n", "    if f.endswith(\".tif\"):\n", "        base_name, channel = f.rsplit('_ch', 1)\n", "        channel = 'ch' + channel.split('.')[0]\n", "        if base_name not in image_sets:\n", "            image_sets[base_name] = {}\n", "        image_sets[base_name][channel] = os.path.join(src_dir, f)\n", "\n", "# Ensure the reference image is excluded from the image sets\n", "image_sets.pop(os.path.basename(reference_slide).rsplit('_ch', 1)[0], None)\n", "\n", "# Parameters for alignment\n", "use_non_rigid = True\n", "use_micro_registration = True\n", "affine_transform_type = \"Affine\"\n", "normalize = False\n", "save_intermediate = True\n", "\n", "align_images(image_sets, reference_image, reference_pixel_size, dst_dir, use_non_rigid, use_micro_registration, affine_transform_type, normalize, save_intermediate)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["VALIS"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/anaconda3/envs/VALIS2/lib/python3.10/site-packages/tqdm/auto.py:21: TqdmWarning: IProgress not found. Please update jupyter and ipywidgets. See https://ipywidgets.readthedocs.io/en/stable/user_install.html\n", "  from .autonotebook import tqdm as notebook_tqdm\n"]}], "source": ["import time\n", "import os\n", "import numpy as np\n", "\n", "from tqdm import tqdm\n", "import sys\n", "import pathlib\n", "from valis import registration, valtils, preprocessing, slide_io\n", "from valis.micro_rigid_registrar import MicroRigidRegistrar # For high resolution rigid registration\n", "\n", "from PIL import Image\n", "import matplotlib.pyplot as plt\n", "import pyvips"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["src_dir = \"/mnt/d/Users/<USER>/antho 4i alignment/LB06_Tiffs/test/\"\n", "dst_dir = \"/mnt/d/Users/<USER>/antho 4i alignment/LB06_Tiffs/test/aligned_images/\"\n", "reference_slide = \"/mnt/d/Users/<USER>/antho 4i alignment/LB06_Tiffs/test/LB06 BP 4i aSMA green cxcr3 red 28mar25_R 3_Merged_ch00\"\n", "micro_reg_fraction = 0.3 # Fraction full resolution used for non-rigid registration"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["#single images , no rounds : \n", "from valis import registration\n", "slide_src_dir = \"/mnt/d/Users/<USER>/antho 4i alignment/LB06\"\n", "results_dst_dir = \"/mnt/d/Users/<USER>/antho 4i alignment/skin dapi hne/slide_registration_example\"\n", "registered_slide_dst_dir = \"/mnt/d/Users/<USER>/antho 4i alignment/skin dapi hne/slide_registration_example/registered_slides\"\n", "reference_slide = \"/mnt/d/Users/<USER>/antho 4i alignment/skin dapi hne/dr3216481 82 male strip 4i_R 1_Merged_ch00.tiff\"\n", "\n", "# Create a Valis object and use it to register the slides in slide_src_dir, aligning *towards* the reference slide.\n", "registrar = registration.Valis(slide_src_dir, results_dst_dir, reference_img_f=reference_slide)\n", "rigid_registrar, non_rigid_registrar, error_df = registrar.register()\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Save all registered slides as ome.tiff\n", "registrar.warp_and_save_slides(registered_slide_dst_dir, crop=\"overlap\")\n", "\n", "# Kill the JVM\n", "registration.kill_jvm()"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["def get_round_name(src_f):\n", "    img_name = valtils.get_name(src_f)\n", "    round_name = img_name.lower().split(\"_merged\")[0]\n", "    return round_name\n", "\n", "# def get_round_name(src_f):\n", "#     # Extract the round name from the filename\n", "#     # Example filename: r02c02f01_round1_ch00.tiff\n", "#     img_name = os.path.basename(src_f)\n", "#     round_part = img_name.split('_round')[1]  # Split by '_round'\n", "#     round_name = round_part.split('_ch')[0]  # Get the part before '_ch'\n", "#     return round_name\n", "\n", "def get_channel_number(src_f):\n", "    \"\"\"Extract the channel number from the filename.\n", "    For filenames like 'LB06 BP 4i aSMA green cxcr3 red 28mar25_R 3_Merged_ch00.tif',\n", "    this returns 0\n", "    \"\"\"\n", "    img_name = os.path.basename(src_f).lower()\n", "    if \"_ch00\" in img_name:\n", "        return 0\n", "    elif \"_ch01\" in img_name:\n", "        return 1\n", "    elif \"_ch02\" in img_name:\n", "        return 2\n", "    else:\n", "        return None\n", "       \n", "def get_ome_xml(warped_slide, reference_slide, channel_names=None):\n", "  \"\"\"Generate ome-xml for warped slide\n", "\n", "  Parameters\n", "  ----------\n", "  warped_slide : pyvips.Image\n", "    Registered slide that will be saved as an ome.tiff\n", "\n", "  reference_slide : registration.Slide\n", "    Slide object that the others were aligned to/towards\n", "\n", "  channel_names : str, list, optional\n", "    channel names for warped slide. If `None`, then the\n", "    channel names from `src_f` will be used\n", "\n", "\n", "  Returns\n", "  -------\n", "  ome_xml : str\n", "    String of the ome-xml metadata\n", "\n", "  \"\"\"\n", "\n", "  ref_meta = reference_slide.reader.metadata\n", "  bf_dtype = slide_io.vips2bf_dtype(warped_slide.format)\n", "  out_xyczt = slide_io.get_shape_xyzct((warped_slide.width, warped_slide.height), warped_slide.bands)\n", "  ome_xml_obj = slide_io.create_ome_xml(\n", "      shape_xyzct=out_xyczt,\n", "      bf_dtype=bf_dtype,\n", "      is_rgb=False,\n", "      pixel_physical_size_xyu=ref_meta.pixel_physical_size_xyu,\n", "      channel_names=channel_names,\n", "      colormap=None\n", "  )\n", "  return ome_xml_obj.to_xml()"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Starting image processing with DIRECT channel handling...\n", "Found 2 DAPI images for registration\n", "Performing registration...\n", "\n", "==== Converting images\n", "\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Converting images:   0%|          | 0/2 [00:00<?, ?image/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["<Slide, name = LB06 BP 4i BCL2 green CD45 red 1apr25_R 1 (2)_Merged_ch00>, width=29579, height=14957, channels=1, levels=1, RGB=False, dtype=uint8> <valis.slide_io.VipsSlideReader object at 0x7fc3247b0490> False (518, 1024)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Converting images:  50%|█████     | 1/2 [00:07<00:07,  7.17s/image]"]}, {"name": "stdout", "output_type": "stream", "text": ["<Slide, name = LB06 BP 4i aSMA green cxcr3 red 28mar25_R 3_Merged_ch00>, width=27767, height=18615, channels=1, levels=1, RGB=False, dtype=uint8> <valis.slide_io.VipsSlideReader object at 0x7fc3247b0340> False (687, 1024)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Converting images: 100%|██████████| 2/2 [00:15<00:00,  7.64s/image]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "==== Processing images\n", "\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Processing images : 100%|██████████| 2/2 [00:16<00:00,  8.05s/image]\n", "Normalizing images: 100%|██████████| 2/2 [00:00<00:00, 14.52image/s]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "==== Rigid registration\n", "\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Detecting features   : 100%|██████████| 2/2 [00:04<00:00,  2.21s/image]\n", "QUEUEING TASKS | Matching images      : 100%|██████████| 2/2 [00:00<00:00, 1074.09image/s]\n", "PROCESSING TASKS | Matching images      : 100%|██████████| 2/2 [00:00<00:00,  3.85image/s]\n", "COLLECTING RESULTS | Matching images      : 100%|██████████| 2/2 [00:00<00:00, 42799.02image/s]\n", "Finding transforms   : 100%|██████████| 1/1 [00:00<00:00, 1313.18image/s]\n", "Finalizing           : 100%|██████████| 2/2 [00:00<00:00, 2478.90image/s]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "======== Rigid registration complete in 5.314 seconds\n", "\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/anaconda3/envs/VALIS2/lib/python3.10/site-packages/valis/viz.py:598: RuntimeWarning: divide by zero encountered in divide\n", "  weight = grey_img_list[i]/sum_img\n", "/home/<USER>/anaconda3/envs/VALIS2/lib/python3.10/site-packages/valis/viz.py:601: RuntimeWarning: invalid value encountered in add\n", "  blended_img += lab_clr * np.dstack([grey_img_list[i]/max_v * weight]*3)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "==== Non-rigid registration\n", "\n", "Creating non-rigid mask\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Preparing images for non-rigid registration: 100%|██████████| 2/2 [00:08<00:00,  4.48s/image]\n", "Finding non-rigid transforms: 100%|██████████| 2/2 [00:01<00:00,  1.14image/s]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "======== Non-rigid registration complete in 1.906 seconds\n", "\n", "\n", "==== Measuring error\n", "\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Measuring error: 100%|██████████| 2/2 [00:00<00:00, 10.89image/s]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Performing micro-registration with size: 8330...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Preparing images for non-rigid registration: 100%|██████████| 2/2 [00:17<00:00,  8.87s/image]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "==== Performing microregistration\n", "\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Finding non-rigid transforms: 100%|██████████| 2/2 [02:09<00:00, 64.92s/image]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "======== Non-rigid registration complete in 2.317 minutes\n", "\n", "\n", "==== Measuring error\n", "\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Measuring error: 100%|██████████| 2/2 [00:19<00:00,  9.55s/image]\n"]}, {"ename": "IndexError", "evalue": "index 1 is out of bounds for axis 0 with size 1", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mIndexError\u001b[0m                                <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[7], line 44\u001b[0m\n\u001b[1;32m     41\u001b[0m channel_names \u001b[38;5;241m=\u001b[39m []\n\u001b[1;32m     42\u001b[0m warped_slide \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01m<PERSON>one\u001b[39;00m\n\u001b[0;32m---> 44\u001b[0m reference_shape \u001b[38;5;241m=\u001b[39m (reference_slide\u001b[38;5;241m.\u001b[39mslide_dimensions_wh[\u001b[38;5;241m0\u001b[39m], \u001b[43mreference_slide\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mslide_dimensions_wh\u001b[49m\u001b[43m[\u001b[49m\u001b[38;5;241;43m1\u001b[39;49m\u001b[43m]\u001b[49m)\n\u001b[1;32m     46\u001b[0m \u001b[38;5;28;01mfor\u001b[39;00m slide_warper, round_slides \u001b[38;5;129;01min\u001b[39;00m tqdm(round_dict\u001b[38;5;241m.\u001b[39mitems()):\n\u001b[1;32m     47\u001b[0m     \u001b[38;5;28mprint\u001b[39m(\u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;130;01m\\n\u001b[39;00m\u001b[38;5;124m[DEBUG] Warping images for round: \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mslide_warper\u001b[38;5;241m.\u001b[39mname\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m\"\u001b[39m)\n", "\u001b[0;31mIndexError\u001b[0m: index 1 is out of bounds for axis 0 with size 1"]}], "source": ["\"\"\"Process images with direct channel handling to ensure proper ordering\"\"\"\n", "print(\"Starting image processing with DIRECT channel handling...\")\n", "\n", "# Create output directory\n", "merged_slide_dir = os.path.join(os.path.split(dst_dir)[0], \"slides\")\n", "pathlib.Path(merged_slide_dir).mkdir(exist_ok=True, parents=True)\n", "\n", "# Directory for individual warped channels\n", "individual_channel_dir = os.path.join(merged_slide_dir, \"warped_channels\")\n", "os.makedirs(individual_channel_dir, exist_ok=True)\n", "\n", "# Get all images\n", "full_img_list = [os.path.join(src_dir, f) for f in os.listdir(src_dir)]\n", "\n", "# Register rounds using DAPI channels , which all end in \"ch00.tif\"\n", "dapi_imgs = [f for f in full_img_list if f.endswith(\"ch00.tif\")]\n", "print(f\"Found {len(dapi_imgs)} DAPI images for registration\")\n", "\n", "# Initialize VALIS registrar\n", "registrar = registration.Valis(\n", "    src_dir, dst_dir, img_list=dapi_imgs,\n", "    reference_img_f=reference_slide,\n", "    micro_rigid_registrar_cls=None,\n", "    align_to_reference=True\n", ")\n", "print(\"Performing registration...\")\n", "rigid_registrar, non_rigid_registrar, error_df = registrar.register()\n", "\n", "reference_slide = registrar.get_ref_slide()\n", "micro_reg_size = np.floor(np.max(reference_slide.slide_dimensions_wh[0]) * micro_reg_fraction).astype(int)\n", "print(f\"Performing micro-registration with size: {micro_reg_size}...\")\n", "micro_reg, micro_error = registrar.register_micro(\n", "    max_non_rigid_registration_dim_px=micro_reg_size,\n", "    align_to_reference=True\n", ")\n", "\n", "# Use registration parameters to warp DAPI channel and the other images from the same round\n", "round_dict = {slide_obj: [f for f in full_img_list if get_round_name(f) == get_round_name(slide_obj.src_f)]\n", "              for slide_obj in registrar.slide_dict.values()}\n", "\n", "channel_names = []\n", "warped_slide = None\n", "\n", "reference_shape = (reference_slide.slide_dimensions_wh[0], reference_slide.slide_dimensions_wh[1])\n", "\n", "for slide_warper, round_slides in tqdm(round_dict.items()):\n", "    print(f\"\\n[DEBUG] Warping images for round: {slide_warper.name}\")\n", "    valtils.sort_nicely(round_slides)\n", "    print(f\"[DEBUG] Files in this round: {round_slides}\")\n", "\n", "    for src_f in round_slides:\n", "        img_name = valtils.get_name(src_f)\n", "        print(f\"[DEBUG] Warping channel: {img_name}\")\n", "        channel_names.append(img_name)\n", "        channel_img = pyvips.Image.new_from_file(src_f)\n", "        print(f\"[DEBUG] Channel image shape: {channel_img.width}x{channel_img.height}, bands: {channel_img.bands}\")\n", "\n", "        # Apply the DAPI transformation to this channel\n", "        warped_channel = slide_warper.warp_img(img=channel_img, crop=False)\n", "        print(f\"[DEBUG] Warped channel shape: {warped_channel.width}x{warped_channel.height}, bands: {warped_channel.bands}\")\n", "\n", "        # Resize warped channel to reference image shape if needed\n", "        if (warped_channel.width, warped_channel.height) != reference_shape:\n", "            scale_x = reference_shape[0] / warped_channel.width\n", "            scale_y = reference_shape[1] / warped_channel.height\n", "            warped_channel = warped_channel.resize(scale_x, vscale=scale_y)\n", "            print(f\"[DEBUG] Resized warped channel to reference shape: {warped_channel.width}x{warped_channel.height}\")\n", "\n", "        # Save each warped channel individually\n", "        channel_basename = os.path.basename(src_f)\n", "        individual_channel_path = os.path.join(individual_channel_dir, f\"warped_{channel_basename}\")\n", "        warped_channel.write_to_file(individual_channel_path)\n", "        print(f\"[DEBUG] Saved individual warped channel: {individual_channel_path}\")\n", "\n", "        if warped_slide is None:\n", "            warped_slide = warped_channel\n", "            print(f\"[DEBUG] Created initial warped_slide with {warped_slide.bands} bands\")\n", "        else:\n", "            warped_slide = warped_slide.bandjoin(warped_channel)\n", "            print(f\"[DEBUG] After bandjoin: {warped_slide.bands} bands in warped_slide\")\n", "\n", "merged_slide_f = os.path.join(merged_slide_dir, \"merged.ome.tiff\")\n", "merged_ome_xml = get_ome_xml(warped_slide, reference_slide, channel_names)\n", "slide_io.save_ome_tiff(img=warped_slide, dst_f=merged_slide_f, ome_xml=merged_ome_xml)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Starting image processing with DIRECT channel handling...\n", "Found 9 DAPI images for registration\n", "Performing registration...\n", "\n", "==== Converting images\n", "\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Converting images:   0%|          | 0/9 [00:00<?, ?image/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["<Slide, name = LB06 BP 4i BCL2 green CD45 red 1apr25_R 1 (2)_Merged_ch00>, width=29579, height=14957, channels=1, levels=1, RGB=False, dtype=uint8> <valis.slide_io.VipsSlideReader object at 0x7fdcd82d35e0> False (518, 1024)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Converting images:  11%|█         | 1/9 [00:07<00:57,  7.23s/image]"]}, {"name": "stdout", "output_type": "stream", "text": ["<Slide, name = LB06 BP 4i CD31 red 4apr25_R 1_Merged_ch00>, width=27740, height=13102, channels=1, levels=1, RGB=False, dtype=uint8> <valis.slide_io.VipsSlideReader object at 0x7fda308d9990> False (484, 1024)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Converting images:  22%|██▏       | 2/9 [00:13<00:45,  6.45s/image]"]}, {"name": "stdout", "output_type": "stream", "text": ["<Slide, name = LB06 BP 4i HMGB1 green PCNA red 26mar25_R 3_Merged_ch00>, width=27754, height=13126, channels=1, levels=1, RGB=False, dtype=uint8> <valis.slide_io.VipsSlideReader object at 0x7fda308db700> False (484, 1024)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Converting images:  33%|███▎      | 3/9 [00:18<00:36,  6.16s/image]"]}, {"name": "stdout", "output_type": "stream", "text": ["<Slide, name = LB06 BP 4i IP10 green IL1a red 3apr25_R 1_Merged_ch00>, width=38788, height=24205, channels=1, levels=1, RGB=False, dtype=uint8> <valis.slide_io.VipsSlideReader object at 0x7fda308d8a00> False (639, 1024)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Converting images:  44%|████▍     | 4/9 [00:31<00:43,  8.62s/image]"]}, {"name": "stdout", "output_type": "stream", "text": ["<Slide, name = LB06 BP 4i LaminB1 green BCL2 red 27mar25_R 1_Merged_ch00>, width=35116, height=18607, channels=1, levels=1, RGB=False, dtype=uint8> <valis.slide_io.VipsSlideReader object at 0x7fda308db6a0> False (543, 1024)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Converting images:  56%|█████▌    | 5/9 [00:40<00:34,  8.68s/image]"]}, {"name": "stdout", "output_type": "stream", "text": ["<Slide, name = LB06 BP 4i MMP9 green 7apr25_R 1_Merged_ch00>, width=24099, height=11301, channels=1, levels=1, RGB=False, dtype=uint8> <valis.slide_io.VipsSlideReader object at 0x7fda308db730> False (480, 1024)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Converting images:  67%|██████▋   | 6/9 [00:44<00:22,  7.35s/image]"]}, {"name": "stdout", "output_type": "stream", "text": ["<Slide, name = LB06 BP 4i aSMA green cxcr3 red 28mar25_R 3_Merged_ch00>, width=27767, height=18615, channels=1, levels=1, RGB=False, dtype=uint8> <valis.slide_io.VipsSlideReader object at 0x7fda308a6f50> False (687, 1024)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Converting images:  78%|███████▊  | 7/9 [00:53<00:15,  7.66s/image]"]}, {"name": "stdout", "output_type": "stream", "text": ["<Slide, name = LB06 BP 4i p21 green p16 red 25mar25_R 6_Merged_ch00>, width=31400, height=13135, channels=1, levels=1, RGB=False, dtype=uint8> <valis.slide_io.VipsSlideReader object at 0x7fda308a6290> False (429, 1024)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Converting images:  89%|████████▉ | 8/9 [00:59<00:07,  7.19s/image]"]}, {"name": "stdout", "output_type": "stream", "text": ["<Slide, name = LB06 BP 4i vimentin green pancytokeratin red 2apr25_R 1_Merged_ch00>, width=27746, height=13131, channels=1, levels=1, RGB=False, dtype=uint8> <valis.slide_io.VipsSlideReader object at 0x7fda308a4070> False (485, 1024)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Converting images: 100%|██████████| 9/9 [01:05<00:00,  7.23s/image]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "==== Processing images\n", "\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Processing images : 100%|██████████| 9/9 [01:09<00:00,  7.69s/image]\n", "Normalizing images: 100%|██████████| 9/9 [00:00<00:00, 15.17image/s]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "==== Rigid registration\n", "\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Detecting features   : 100%|██████████| 9/9 [00:15<00:00,  1.67s/image]\n", "QUEUEING TASKS | Matching images      : 100%|██████████| 9/9 [00:00<00:00, 171.34image/s]\n", "PROCESSING TASKS | Matching images      : 100%|██████████| 9/9 [00:08<00:00,  1.12image/s]\n", "COLLECTING RESULTS | Matching images      : 100%|██████████| 9/9 [00:00<00:00, 125829.12image/s]\n", "Finding transforms   : 100%|██████████| 8/8 [00:00<00:00, 1074.77image/s]\n", "Finalizing           : 100%|██████████| 9/9 [00:00<00:00, 5407.35image/s]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "======== Rigid registration complete in 24.758 seconds\n", "\n", "\n", "==== Non-rigid registration\n", "\n", "Creating non-rigid mask\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Preparing images for non-rigid registration: 100%|██████████| 9/9 [00:39<00:00,  4.40s/image]\n", "Finding non-rigid transforms: 100%|██████████| 9/9 [00:15<00:00,  1.69s/image]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "======== Non-rigid registration complete in 15.463 seconds\n", "\n", "\n", "==== Measuring error\n", "\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Measuring error: 100%|██████████| 9/9 [00:01<00:00,  5.66image/s]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Performing micro-registration with size: 8330...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Preparing images for non-rigid registration: 100%|██████████| 9/9 [00:58<00:00,  6.51s/image]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "==== Performing microregistration\n", "\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Finding non-rigid transforms: 100%|██████████| 9/9 [18:10<00:00, 121.22s/image]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "======== Non-rigid registration complete in 18.338 minutes\n", "\n", "\n", "==== Measuring error\n", "\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Measuring error: 100%|██████████| 9/9 [02:53<00:00, 19.31s/image]\n", "  0%|          | 0/9 [00:00<?, ?it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "[DEBUG] Warping images for round: LB06 BP 4i BCL2 green CD45 red 1apr25_R 1 (2)_Merged_ch00\n", "[DEBUG] Files in this round: ['/mnt/d/Users/<USER>/antho 4i alignment/LB06_Tiffs/8bits/LB06 BP 4i BCL2 green CD45 red 1apr25_R 1 (2)_Merged_ch00.tif', '/mnt/d/Users/<USER>/antho 4i alignment/LB06_Tiffs/8bits/LB06 BP 4i BCL2 green CD45 red 1apr25_R 1 (2)_Merged_ch01.tif', '/mnt/d/Users/<USER>/antho 4i alignment/LB06_Tiffs/8bits/LB06 BP 4i BCL2 green CD45 red 1apr25_R 1 (2)_Merged_ch02.tif']\n", "[DEBUG] Transformation M: [[  9.37982573e-01  -1.79837023e-03  -2.99393087e+02]\n", " [  1.79837023e-03   9.37982573e-01  -8.90189085e+00]\n", " [  0.00000000e+00   0.00000000e+00   1.00000000e+00]]\n", "[DEBUG] Non-rigid bk_dxdy: [[[   0.           0.           0.        ...,    0.           0.\n", "      0.       ]\n", "  [   0.           0.           0.        ...,    0.           0.\n", "      0.       ]\n", "  [   0.           0.           0.        ...,    0.           0.\n", "      0.       ]\n", "  ..., \n", "  [   0.           0.           0.        ...,   -2.9604216   -2.9603386\n", "     -2.9602575]\n", "  [   0.           0.           0.        ...,   -2.9604654   -2.9603834\n", "     -2.9603033]\n", "  [   0.           0.           0.        ...,   -2.9605026   -2.9604235\n", "     -2.9603434]]\n", "\n", " [[   0.           0.           0.        ...,    0.           0.\n", "      0.       ]\n", "  [   0.           0.           0.        ...,    0.           0.\n", "      0.       ]\n", "  [   0.           0.           0.        ...,    0.           0.\n", "      0.       ]\n", "  ..., \n", "  [   0.           0.           0.        ..., -130.44995   -130.44797\n", "   -130.44598  ]\n", "  [   0.           0.           0.        ..., -130.44992   -130.44794\n", "   -130.44598  ]\n", "  [   0.           0.           0.        ..., -130.44989   -130.44794\n", "   -130.44592  ]]]\n", "[DEBUG] Source shape: [ 518 1024], Dest shape: [ 951 1470], Out shape: [25769 39861]\n", "[DEBUG] Warping channel: LB06 BP 4i BCL2 green CD45 red 1apr25_R 1 (2)_Merged_ch00\n", "[DEBUG] Channel image shape: 29579x14957, bands: 1\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/anaconda3/envs/VALIS2/lib/python3.10/site-packages/valis/valtils.py:25: UserWarning: scaling transformation for image with different shape. However, without knowing all of other image's shapes, the scaling may not be the same for all images, and so may not overlap.\n", "  warnings.warn(warning_msg, warning_type)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[DEBUG] Warped channel shape: 21472x54305, bands: 1\n", "[DEBUG] Created initial warped_slide with 1 bands\n", "[DEBUG] Warping channel: LB06 BP 4i BCL2 green CD45 red 1apr25_R 1 (2)_Merged_ch01\n", "[DEBUG] Channel image shape: 29579x14957, bands: 1\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/anaconda3/envs/VALIS2/lib/python3.10/site-packages/valis/valtils.py:25: UserWarning: scaling transformation for image with different shape. However, without knowing all of other image's shapes, the scaling may not be the same for all images, and so may not overlap.\n", "  warnings.warn(warning_msg, warning_type)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[DEBUG] Warped channel shape: 21472x54305, bands: 1\n", "[DEBUG] After bandjoin: 2 bands in warped_slide\n", "[DEBUG] Warping channel: LB06 BP 4i BCL2 green CD45 red 1apr25_R 1 (2)_Merged_ch02\n", "[DEBUG] Channel image shape: 29579x14957, bands: 1\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/anaconda3/envs/VALIS2/lib/python3.10/site-packages/valis/valtils.py:25: UserWarning: scaling transformation for image with different shape. However, without knowing all of other image's shapes, the scaling may not be the same for all images, and so may not overlap.\n", "  warnings.warn(warning_msg, warning_type)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[DEBUG] Warped channel shape: 21472x54305, bands: 1\n", "[DEBUG] After bandjoin: 3 bands in warped_slide\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 11%|█         | 1/9 [00:02<00:16,  2.04s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "[DEBUG] Warping images for round: LB06 BP 4i CD31 red 4apr25_R 1_Merged_ch00\n", "[DEBUG] Files in this round: ['/mnt/d/Users/<USER>/antho 4i alignment/LB06_Tiffs/8bits/LB06 BP 4i CD31 red 4apr25_R 1_Merged_ch00.tif', '/mnt/d/Users/<USER>/antho 4i alignment/LB06_Tiffs/8bits/LB06 BP 4i CD31 red 4apr25_R 1_Merged_ch01.tif']\n", "[DEBUG] Transformation M: [[  9.99904849e-01  -6.77342579e-03  -3.56843562e+02]\n", " [  6.77342579e-03   9.99904849e-01  -9.35393144e+00]\n", " [  0.00000000e+00   0.00000000e+00   1.00000000e+00]]\n", "[DEBUG] Non-rigid bk_dxdy: [[[   0.         0.         0.      ...,    0.         0.         0.     ]\n", "  [   0.         0.         0.      ...,    0.         0.         0.     ]\n", "  [   0.         0.         0.      ...,    0.         0.         0.     ]\n", "  ..., \n", "  [   0.         0.         0.      ..., -191.32922 -191.32976 -191.3303 ]\n", "  [   0.         0.         0.      ..., -191.32918 -191.32973 -191.33026]\n", "  [   0.         0.         0.      ..., -191.32916 -191.3297  -191.33023]]\n", "\n", " [[   0.         0.         0.      ...,    0.         0.         0.     ]\n", "  [   0.         0.         0.      ...,    0.         0.         0.     ]\n", "  [   0.         0.         0.      ...,    0.         0.         0.     ]\n", "  ..., \n", "  [   0.         0.         0.      ..., -694.75055 -694.7482  -694.746  ]\n", "  [   0.         0.         0.      ..., -694.75055 -694.7482  -694.74585]\n", "  [   0.         0.         0.      ..., -694.7505  -694.74817 -694.74585]]]\n", "[DEBUG] Source shape: [ 484 1024], Dest shape: [ 951 1470], Out shape: [25769 39861]\n", "[DEBUG] Warping channel: LB06 BP 4i CD31 red 4apr25_R 1_Merged_ch00\n", "[DEBUG] Channel image shape: 27740x13102, bands: 1\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/anaconda3/envs/VALIS2/lib/python3.10/site-packages/valis/valtils.py:25: UserWarning: scaling transformation for image with different shape. However, without knowing all of other image's shapes, the scaling may not be the same for all images, and so may not overlap.\n", "  warnings.warn(warning_msg, warning_type)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[DEBUG] Warped channel shape: 18809x54506, bands: 1\n", "[DEBUG] After bandjoin: 4 bands in warped_slide\n", "[DEBUG] Warping channel: LB06 BP 4i CD31 red 4apr25_R 1_Merged_ch01\n", "[DEBUG] Channel image shape: 27740x13102, bands: 1\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/anaconda3/envs/VALIS2/lib/python3.10/site-packages/valis/valtils.py:25: UserWarning: scaling transformation for image with different shape. However, without knowing all of other image's shapes, the scaling may not be the same for all images, and so may not overlap.\n", "  warnings.warn(warning_msg, warning_type)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[DEBUG] Warped channel shape: 18809x54506, bands: 1\n", "[DEBUG] After bandjoin: 5 bands in warped_slide\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 22%|██▏       | 2/9 [00:03<00:12,  1.78s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "[DEBUG] Warping images for round: LB06 BP 4i HMGB1 green PCNA red 26mar25_R 3_Merged_ch00\n", "[DEBUG] Files in this round: ['/mnt/d/Users/<USER>/antho 4i alignment/LB06_Tiffs/8bits/LB06 BP 4i HMGB1 green PCNA red 26mar25_R 3_Merged_ch00.tif', '/mnt/d/Users/<USER>/antho 4i alignment/LB06_Tiffs/8bits/LB06 BP 4i HMGB1 green PCNA red 26mar25_R 3_Merged_ch01.tif', '/mnt/d/Users/<USER>/antho 4i alignment/LB06_Tiffs/8bits/LB06 BP 4i HMGB1 green PCNA red 26mar25_R 3_Merged_ch02.tif']\n", "[DEBUG] Transformation M: [[  9.99535372e-01  -3.78158767e-04  -3.56246757e+02]\n", " [  3.78158767e-04   9.99535372e-01  -1.41771231e+01]\n", " [  0.00000000e+00   0.00000000e+00   1.00000000e+00]]\n", "[DEBUG] Non-rigid bk_dxdy: [[[   0.          0.          0.       ...,    0.          0.\n", "      0.      ]\n", "  [   0.          0.          0.       ...,    0.          0.\n", "      0.      ]\n", "  [   0.          0.          0.       ...,    0.          0.\n", "      0.      ]\n", "  ..., \n", "  [   0.          0.          0.       ..., -113.707985 -113.708244\n", "   -113.7085  ]\n", "  [   0.          0.          0.       ..., -113.707985 -113.708244\n", "   -113.7085  ]\n", "  [   0.          0.          0.       ..., -113.707985 -113.70825\n", "   -113.70851 ]]\n", "\n", " [[   0.          0.          0.       ...,    0.          0.\n", "      0.      ]\n", "  [   0.          0.          0.       ...,    0.          0.\n", "      0.      ]\n", "  [   0.          0.          0.       ...,    0.          0.\n", "      0.      ]\n", "  ..., \n", "  [   0.          0.          0.       ..., -619.42645  -619.424\n", "   -619.4216  ]\n", "  [   0.          0.          0.       ..., -619.42645  -619.424\n", "   -619.4216  ]\n", "  [   0.          0.          0.       ..., -619.42645  -619.424\n", "   -619.4216  ]]]\n", "[DEBUG] Source shape: [ 484 1024], Dest shape: [ 951 1470], Out shape: [25769 39861]\n", "[DEBUG] Warping channel: LB06 BP 4i HMGB1 green PCNA red 26mar25_R 3_Merged_ch00\n", "[DEBUG] Channel image shape: 27754x13126, bands: 1\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/anaconda3/envs/VALIS2/lib/python3.10/site-packages/valis/valtils.py:25: UserWarning: scaling transformation for image with different shape. However, without knowing all of other image's shapes, the scaling may not be the same for all images, and so may not overlap.\n", "  warnings.warn(warning_msg, warning_type)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[DEBUG] Warped channel shape: 18843x54534, bands: 1\n", "[DEBUG] After bandjoin: 6 bands in warped_slide\n", "[DEBUG] Warping channel: LB06 BP 4i HMGB1 green PCNA red 26mar25_R 3_Merged_ch01\n", "[DEBUG] Channel image shape: 27754x13126, bands: 1\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/anaconda3/envs/VALIS2/lib/python3.10/site-packages/valis/valtils.py:25: UserWarning: scaling transformation for image with different shape. However, without knowing all of other image's shapes, the scaling may not be the same for all images, and so may not overlap.\n", "  warnings.warn(warning_msg, warning_type)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[DEBUG] Warped channel shape: 18843x54534, bands: 1\n", "[DEBUG] After bandjoin: 7 bands in warped_slide\n", "[DEBUG] Warping channel: LB06 BP 4i HMGB1 green PCNA red 26mar25_R 3_Merged_ch02\n", "[DEBUG] Channel image shape: 27754x13126, bands: 1\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/anaconda3/envs/VALIS2/lib/python3.10/site-packages/valis/valtils.py:25: UserWarning: scaling transformation for image with different shape. However, without knowing all of other image's shapes, the scaling may not be the same for all images, and so may not overlap.\n", "  warnings.warn(warning_msg, warning_type)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[DEBUG] Warped channel shape: 18843x54534, bands: 1\n", "[DEBUG] After bandjoin: 8 bands in warped_slide\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 33%|███▎      | 3/9 [00:05<00:12,  2.03s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "[DEBUG] Warping images for round: LB06 BP 4i IP10 green IL1a red 3apr25_R 1_Merged_ch00\n", "[DEBUG] Files in this round: ['/mnt/d/Users/<USER>/antho 4i alignment/LB06_Tiffs/8bits/LB06 BP 4i IP10 green IL1a red 3apr25_R 1_Merged_ch00.tif', '/mnt/d/Users/<USER>/antho 4i alignment/LB06_Tiffs/8bits/LB06 BP 4i IP10 green IL1a red 3apr25_R 1_Merged_ch01.tif', '/mnt/d/Users/<USER>/antho 4i alignment/LB06_Tiffs/8bits/LB06 BP 4i IP10 green IL1a red 3apr25_R 1_Merged_ch02.tif']\n", "[DEBUG] Transformation M: [[  7.16453801e-01   1.74439367e-03  -1.65243600e+00]\n", " [ -1.74439367e-03   7.16453801e-01  -3.96851374e+01]\n", " [  0.00000000e+00   0.00000000e+00   1.00000000e+00]]\n", "[DEBUG] Non-rigid bk_dxdy: [[[   0.         0.         0.      ...,    0.         0.         0.     ]\n", "  [   0.         0.         0.      ...,    0.         0.         0.     ]\n", "  [   0.         0.         0.      ...,    0.         0.         0.     ]\n", "  ..., \n", "  [   0.         0.         0.      ...,  551.1572   551.1587   551.16003]\n", "  [   0.         0.         0.      ...,  551.15717  551.15857  551.16   ]\n", "  [   0.         0.         0.      ...,  551.15704  551.15845  551.15985]]\n", "\n", " [[   0.         0.         0.      ...,    0.         0.         0.     ]\n", "  [   0.         0.         0.      ...,    0.         0.         0.     ]\n", "  [   0.         0.         0.      ...,    0.         0.         0.     ]\n", "  ..., \n", "  [   0.         0.         0.      ...,  752.5564   752.55664  752.5569 ]\n", "  [   0.         0.         0.      ...,  752.5563   752.5566   752.5568 ]\n", "  [   0.         0.         0.      ...,  752.5562   752.55646  752.55676]]]\n", "[DEBUG] Source shape: [ 639 1024], Dest shape: [ 951 1470], Out shape: [25769 39861]\n", "[DEBUG] Warping channel: LB06 BP 4i IP10 green IL1a red 3apr25_R 1_Merged_ch00\n", "[DEBUG] Channel image shape: 38788x24205, bands: 1\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/anaconda3/envs/VALIS2/lib/python3.10/site-packages/valis/valtils.py:25: UserWarning: scaling transformation for image with different shape. However, without knowing all of other image's shapes, the scaling may not be the same for all images, and so may not overlap.\n", "  warnings.warn(warning_msg, warning_type)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[DEBUG] Warped channel shape: 34748x57727, bands: 1\n", "[DEBUG] After bandjoin: 9 bands in warped_slide\n", "[DEBUG] Warping channel: LB06 BP 4i IP10 green IL1a red 3apr25_R 1_Merged_ch01\n", "[DEBUG] Channel image shape: 38788x24205, bands: 1\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/anaconda3/envs/VALIS2/lib/python3.10/site-packages/valis/valtils.py:25: UserWarning: scaling transformation for image with different shape. However, without knowing all of other image's shapes, the scaling may not be the same for all images, and so may not overlap.\n", "  warnings.warn(warning_msg, warning_type)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[DEBUG] Warped channel shape: 34748x57727, bands: 1\n", "[DEBUG] After bandjoin: 10 bands in warped_slide\n", "[DEBUG] Warping channel: LB06 BP 4i IP10 green IL1a red 3apr25_R 1_Merged_ch02\n", "[DEBUG] Channel image shape: 38788x24205, bands: 1\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/anaconda3/envs/VALIS2/lib/python3.10/site-packages/valis/valtils.py:25: UserWarning: scaling transformation for image with different shape. However, without knowing all of other image's shapes, the scaling may not be the same for all images, and so may not overlap.\n", "  warnings.warn(warning_msg, warning_type)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[DEBUG] Warped channel shape: 34748x57727, bands: 1\n", "[DEBUG] After bandjoin: 11 bands in warped_slide\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 44%|████▍     | 4/9 [00:08<00:10,  2.16s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "[DEBUG] Warping images for round: LB06 BP 4i LaminB1 green BCL2 red 27mar25_R 1_Merged_ch00\n", "[DEBUG] Files in this round: ['/mnt/d/Users/<USER>/antho 4i alignment/LB06_Tiffs/8bits/LB06 BP 4i LaminB1 green BCL2 red 27mar25_R 1_Merged_ch00.tif', '/mnt/d/Users/<USER>/antho 4i alignment/LB06_Tiffs/8bits/LB06 BP 4i LaminB1 green BCL2 red 27mar25_R 1_Merged_ch01.tif', '/mnt/d/Users/<USER>/antho 4i alignment/LB06_Tiffs/8bits/LB06 BP 4i LaminB1 green BCL2 red 27mar25_R 1_Merged_ch02.tif']\n", "[DEBUG] Transformation M: [[  7.90606055e-01   4.48922939e-03  -8.32146654e+01]\n", " [ -4.48922939e-03   7.90606055e-01  -2.54856992e+00]\n", " [  0.00000000e+00   0.00000000e+00   1.00000000e+00]]\n", "[DEBUG] Non-rigid bk_dxdy: [[[   0.         0.         0.      ...,    0.         0.         0.     ]\n", "  [   0.         0.         0.      ...,    0.         0.         0.     ]\n", "  [   0.         0.         0.      ...,    0.         0.         0.     ]\n", "  ..., \n", "  [   0.         0.         0.      ...,  444.0761   444.0767   444.0773 ]\n", "  [   0.         0.         0.      ...,  444.0761   444.07666  444.07727]\n", "  [   0.         0.         0.      ...,  444.07608  444.07666  444.07727]]\n", "\n", " [[   0.         0.         0.      ...,    0.         0.         0.     ]\n", "  [   0.         0.         0.      ...,    0.         0.         0.     ]\n", "  [   0.         0.         0.      ...,    0.         0.         0.     ]\n", "  ..., \n", "  [   0.         0.         0.      ...,  597.7325   597.73315  597.73376]\n", "  [   0.         0.         0.      ...,  597.7325   597.73315  597.73376]\n", "  [   0.         0.         0.      ...,  597.7325   597.73315  597.73376]]]\n", "[DEBUG] Source shape: [ 543 1024], Dest shape: [ 951 1470], Out shape: [25769 39861]\n", "[DEBUG] Warping channel: LB06 BP 4i LaminB1 green BCL2 red 27mar25_R 1_Merged_ch00\n", "[DEBUG] Channel image shape: 35116x18607, bands: 1\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/anaconda3/envs/VALIS2/lib/python3.10/site-packages/valis/valtils.py:25: UserWarning: scaling transformation for image with different shape. However, without knowing all of other image's shapes, the scaling may not be the same for all images, and so may not overlap.\n", "  warnings.warn(warning_msg, warning_type)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[DEBUG] Warped channel shape: 26712x61502, bands: 1\n", "[DEBUG] After bandjoin: 12 bands in warped_slide\n", "[DEBUG] Warping channel: LB06 BP 4i LaminB1 green BCL2 red 27mar25_R 1_Merged_ch01\n", "[DEBUG] Channel image shape: 35116x18607, bands: 1\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/anaconda3/envs/VALIS2/lib/python3.10/site-packages/valis/valtils.py:25: UserWarning: scaling transformation for image with different shape. However, without knowing all of other image's shapes, the scaling may not be the same for all images, and so may not overlap.\n", "  warnings.warn(warning_msg, warning_type)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[DEBUG] Warped channel shape: 26712x61502, bands: 1\n", "[DEBUG] After bandjoin: 13 bands in warped_slide\n", "[DEBUG] Warping channel: LB06 BP 4i LaminB1 green BCL2 red 27mar25_R 1_Merged_ch02\n", "[DEBUG] Channel image shape: 35116x18607, bands: 1\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/anaconda3/envs/VALIS2/lib/python3.10/site-packages/valis/valtils.py:25: UserWarning: scaling transformation for image with different shape. However, without knowing all of other image's shapes, the scaling may not be the same for all images, and so may not overlap.\n", "  warnings.warn(warning_msg, warning_type)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[DEBUG] Warped channel shape: 26712x61502, bands: 1\n", "[DEBUG] After bandjoin: 14 bands in warped_slide\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 56%|█████▌    | 5/9 [00:10<00:09,  2.28s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "[DEBUG] Warping images for round: LB06 BP 4i MMP9 green 7apr25_R 1_Merged_ch00\n", "[DEBUG] Files in this round: ['/mnt/d/Users/<USER>/antho 4i alignment/LB06_Tiffs/8bits/LB06 BP 4i MMP9 green 7apr25_R 1_Merged_ch00.tif', '/mnt/d/Users/<USER>/antho 4i alignment/LB06_Tiffs/8bits/LB06 BP 4i MMP9 green 7apr25_R 1_Merged_ch01.tif']\n", "[DEBUG] Transformation M: [[  1.15183530e+00  -6.03546367e-04  -5.19345598e+02]\n", " [  6.03546367e-04   1.15183530e+00  -2.65873918e+01]\n", " [  0.00000000e+00   0.00000000e+00   1.00000000e+00]]\n", "[DEBUG] Non-rigid bk_dxdy: [[[    0.          0.          0.      ...,     0.          0.\n", "       0.     ]\n", "  [    0.          0.          0.      ...,     0.          0.\n", "       0.     ]\n", "  [    0.          0.          0.      ...,     0.          0.\n", "       0.     ]\n", "  ..., \n", "  [    0.          0.          0.      ...,  -203.44078  -203.44128\n", "    -203.44179]\n", "  [    0.          0.          0.      ...,  -203.44072  -203.44124\n", "    -203.44174]\n", "  [    0.          0.          0.      ...,  -203.44067  -203.4412\n", "    -203.44168]]\n", "\n", " [[    0.          0.          0.      ...,     0.          0.\n", "       0.     ]\n", "  [    0.          0.          0.      ...,     0.          0.\n", "       0.     ]\n", "  [    0.          0.          0.      ...,     0.          0.\n", "       0.     ]\n", "  ..., \n", "  [    0.          0.          0.      ..., -1014.8303  -1014.82776\n", "   -1014.8253 ]\n", "  [    0.          0.          0.      ..., -1014.8303  -1014.82776\n", "   -1014.82526]\n", "  [    0.          0.          0.      ..., -1014.8302  -1014.82776\n", "   -1014.82526]]]\n", "[DEBUG] Source shape: [ 480 1024], Dest shape: [ 951 1470], Out shape: [25769 39861]\n", "[DEBUG] Warping channel: LB06 BP 4i MMP9 green 7apr25_R 1_Merged_ch00\n", "[DEBUG] Channel image shape: 24099x11301, bands: 1\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/anaconda3/envs/VALIS2/lib/python3.10/site-packages/valis/valtils.py:25: UserWarning: scaling transformation for image with different shape. However, without knowing all of other image's shapes, the scaling may not be the same for all images, and so may not overlap.\n", "  warnings.warn(warning_msg, warning_type)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[DEBUG] Warped channel shape: 16224x47747, bands: 1\n", "[DEBUG] After bandjoin: 15 bands in warped_slide\n", "[DEBUG] Warping channel: LB06 BP 4i MMP9 green 7apr25_R 1_Merged_ch01\n", "[DEBUG] Channel image shape: 24099x11301, bands: 1\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/anaconda3/envs/VALIS2/lib/python3.10/site-packages/valis/valtils.py:25: UserWarning: scaling transformation for image with different shape. However, without knowing all of other image's shapes, the scaling may not be the same for all images, and so may not overlap.\n", "  warnings.warn(warning_msg, warning_type)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[DEBUG] Warped channel shape: 16224x47747, bands: 1\n", "[DEBUG] After bandjoin: 16 bands in warped_slide\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 67%|██████▋   | 6/9 [00:12<00:06,  2.08s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "[DEBUG] Warping images for round: LB06 BP 4i aSMA green cxcr3 red 28mar25_R 3_Merged_ch00\n", "[DEBUG] Files in this round: ['/mnt/d/Users/<USER>/antho 4i alignment/LB06_Tiffs/8bits/LB06 BP 4i aSMA green cxcr3 red 28mar25_R 3_Merged_ch00.tif', '/mnt/d/Users/<USER>/antho 4i alignment/LB06_Tiffs/8bits/LB06 BP 4i aSMA green cxcr3 red 28mar25_R 3_Merged_ch01.tif', '/mnt/d/Users/<USER>/antho 4i alignment/LB06_Tiffs/8bits/LB06 BP 4i aSMA green cxcr3 red 28mar25_R 3_Merged_ch02.tif']\n", "[DEBUG] Transformation M: [[   1.            0.         -337.42299177]\n", " [   0.            1.          -26.51126434]\n", " [   0.            0.            1.        ]]\n", "[DEBUG] Non-rigid bk_dxdy: [[[ 0.  0.  0. ...,  0.  0.  0.]\n", "  [ 0.  0.  0. ...,  0.  0.  0.]\n", "  [ 0.  0.  0. ...,  0.  0.  0.]\n", "  ..., \n", "  [ 0.  0.  0. ...,  0.  0.  0.]\n", "  [ 0.  0.  0. ...,  0.  0.  0.]\n", "  [ 0.  0.  0. ...,  0.  0.  0.]]\n", "\n", " [[ 0.  0.  0. ...,  0.  0.  0.]\n", "  [ 0.  0.  0. ...,  0.  0.  0.]\n", "  [ 0.  0.  0. ...,  0.  0.  0.]\n", "  ..., \n", "  [ 0.  0.  0. ...,  0.  0.  0.]\n", "  [ 0.  0.  0. ...,  0.  0.  0.]\n", "  [ 0.  0.  0. ...,  0.  0.  0.]]]\n", "[DEBUG] Source shape: [ 687 1024], Dest shape: [ 951 1470], Out shape: [25769 39861]\n", "[DEBUG] Warping channel: LB06 BP 4i aSMA green cxcr3 red 28mar25_R 3_Merged_ch00\n", "[DEBUG] Channel image shape: 27767x18615, bands: 1\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/anaconda3/envs/VALIS2/lib/python3.10/site-packages/valis/valtils.py:25: UserWarning: scaling transformation for image with different shape. However, without knowing all of other image's shapes, the scaling may not be the same for all images, and so may not overlap.\n", "  warnings.warn(warning_msg, warning_type)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[DEBUG] Warped channel shape: 26723x38438, bands: 1\n", "[DEBUG] After bandjoin: 17 bands in warped_slide\n", "[DEBUG] Warping channel: LB06 BP 4i aSMA green cxcr3 red 28mar25_R 3_Merged_ch01\n", "[DEBUG] Channel image shape: 27767x18615, bands: 1\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/anaconda3/envs/VALIS2/lib/python3.10/site-packages/valis/valtils.py:25: UserWarning: scaling transformation for image with different shape. However, without knowing all of other image's shapes, the scaling may not be the same for all images, and so may not overlap.\n", "  warnings.warn(warning_msg, warning_type)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[DEBUG] Warped channel shape: 26723x38438, bands: 1\n", "[DEBUG] After bandjoin: 18 bands in warped_slide\n", "[DEBUG] Warping channel: LB06 BP 4i aSMA green cxcr3 red 28mar25_R 3_Merged_ch02\n", "[DEBUG] Channel image shape: 27767x18615, bands: 1\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/anaconda3/envs/VALIS2/lib/python3.10/site-packages/valis/valtils.py:25: UserWarning: scaling transformation for image with different shape. However, without knowing all of other image's shapes, the scaling may not be the same for all images, and so may not overlap.\n", "  warnings.warn(warning_msg, warning_type)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[DEBUG] Warped channel shape: 26723x38438, bands: 1\n", "[DEBUG] After bandjoin: 19 bands in warped_slide\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 78%|███████▊  | 7/9 [00:15<00:04,  2.44s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "[DEBUG] Warping images for round: LB06 BP 4i p21 green p16 red 25mar25_R 6_Merged_ch00\n", "[DEBUG] Files in this round: ['/mnt/d/Users/<USER>/antho 4i alignment/LB06_Tiffs/8bits/LB06 BP 4i p21 green p16 red 25mar25_R 6_Merged_ch00.tif', '/mnt/d/Users/<USER>/antho 4i alignment/LB06_Tiffs/8bits/LB06 BP 4i p21 green p16 red 25mar25_R 6_Merged_ch01.tif', '/mnt/d/Users/<USER>/antho 4i alignment/LB06_Tiffs/8bits/LB06 BP 4i p21 green p16 red 25mar25_R 6_Merged_ch02.tif']\n", "[DEBUG] Transformation M: [[  8.84278478e-01  -2.81403745e-03  -2.73999794e+02]\n", " [  2.81403745e-03   8.84278478e-01  -2.53162150e+01]\n", " [  0.00000000e+00   0.00000000e+00   1.00000000e+00]]\n", "[DEBUG] Non-rigid bk_dxdy: [[[   0.          0.          0.       ...,    0.          0.\n", "      0.      ]\n", "  [   0.          0.          0.       ...,    0.          0.\n", "      0.      ]\n", "  [   0.          0.          0.       ...,    0.          0.\n", "      0.      ]\n", "  ..., \n", "  [   0.          0.          0.       ..., -106.1609   -106.16074\n", "   -106.16059 ]\n", "  [   0.          0.          0.       ..., -106.160904 -106.16075\n", "   -106.1606  ]\n", "  [   0.          0.          0.       ..., -106.16093  -106.160774\n", "   -106.16062 ]]\n", "\n", " [[   0.          0.          0.       ...,    0.          0.\n", "      0.      ]\n", "  [   0.          0.          0.       ...,    0.          0.\n", "      0.      ]\n", "  [   0.          0.          0.       ...,    0.          0.\n", "      0.      ]\n", "  ..., \n", "  [   0.          0.          0.       ..., -465.967    -465.96417\n", "   -465.9614  ]\n", "  [   0.          0.          0.       ..., -465.96698  -465.96417\n", "   -465.9614  ]\n", "  [   0.          0.          0.       ..., -465.96698  -465.96417\n", "   -465.9614  ]]]\n", "[DEBUG] Source shape: [ 429 1024], Dest shape: [ 951 1470], Out shape: [25769 39861]\n", "[DEBUG] Warping channel: LB06 BP 4i p21 green p16 red 25mar25_R 6_Merged_ch00\n", "[DEBUG] Channel image shape: 31400x13135, bands: 1\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/anaconda3/envs/VALIS2/lib/python3.10/site-packages/valis/valtils.py:25: UserWarning: scaling transformation for image with different shape. However, without knowing all of other image's shapes, the scaling may not be the same for all images, and so may not overlap.\n", "  warnings.warn(warning_msg, warning_type)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[DEBUG] Warped channel shape: 18856x69607, bands: 1\n", "[DEBUG] After bandjoin: 20 bands in warped_slide\n", "[DEBUG] Warping channel: LB06 BP 4i p21 green p16 red 25mar25_R 6_Merged_ch01\n", "[DEBUG] Channel image shape: 31400x13135, bands: 1\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/anaconda3/envs/VALIS2/lib/python3.10/site-packages/valis/valtils.py:25: UserWarning: scaling transformation for image with different shape. However, without knowing all of other image's shapes, the scaling may not be the same for all images, and so may not overlap.\n", "  warnings.warn(warning_msg, warning_type)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[DEBUG] Warped channel shape: 18856x69607, bands: 1\n", "[DEBUG] After bandjoin: 21 bands in warped_slide\n", "[DEBUG] Warping channel: LB06 BP 4i p21 green p16 red 25mar25_R 6_Merged_ch02\n", "[DEBUG] Channel image shape: 31400x13135, bands: 1\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/anaconda3/envs/VALIS2/lib/python3.10/site-packages/valis/valtils.py:25: UserWarning: scaling transformation for image with different shape. However, without knowing all of other image's shapes, the scaling may not be the same for all images, and so may not overlap.\n", "  warnings.warn(warning_msg, warning_type)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[DEBUG] Warped channel shape: 18856x69607, bands: 1\n", "[DEBUG] After bandjoin: 22 bands in warped_slide\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 89%|████████▉ | 8/9 [00:17<00:02,  2.39s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "[DEBUG] Warping images for round: LB06 BP 4i vimentin green pancytokeratin red 2apr25_R 1_Merged_ch00\n", "[DEBUG] Files in this round: ['/mnt/d/Users/<USER>/antho 4i alignment/LB06_Tiffs/8bits/LB06 BP 4i vimentin green pancytokeratin red 2apr25_R 1_Merged_ch00.tif', '/mnt/d/Users/<USER>/antho 4i alignment/LB06_Tiffs/8bits/LB06 BP 4i vimentin green pancytokeratin red 2apr25_R 1_Merged_ch01.tif', '/mnt/d/Users/<USER>/antho 4i alignment/LB06_Tiffs/8bits/LB06 BP 4i vimentin green pancytokeratin red 2apr25_R 1_Merged_ch02.tif']\n", "[DEBUG] Transformation M: [[  1.00005126e+00   4.43829597e-03  -3.78588209e+02]\n", " [ -4.43829597e-03   1.00005126e+00  -2.26657033e+00]\n", " [  0.00000000e+00   0.00000000e+00   1.00000000e+00]]\n", "[DEBUG] Non-rigid bk_dxdy: [[[   0.          0.          0.       ...,    0.          0.\n", "      0.      ]\n", "  [   0.          0.          0.       ...,    0.          0.\n", "      0.      ]\n", "  [   0.          0.          0.       ...,    0.          0.\n", "      0.      ]\n", "  ..., \n", "  [   0.          0.          0.       ...,  -64.740845  -64.74107\n", "    -64.7413  ]\n", "  [   0.          0.          0.       ...,  -64.74086   -64.74109\n", "    -64.74131 ]\n", "  [   0.          0.          0.       ...,  -64.74087   -64.7411\n", "    -64.74132 ]]\n", "\n", " [[   0.          0.          0.       ...,    0.          0.\n", "      0.      ]\n", "  [   0.          0.          0.       ...,    0.          0.\n", "      0.      ]\n", "  [   0.          0.          0.       ...,    0.          0.\n", "      0.      ]\n", "  ..., \n", "  [   0.          0.          0.       ..., -697.6707   -697.6684\n", "   -697.66614 ]\n", "  [   0.          0.          0.       ..., -697.6708   -697.66846\n", "   -697.6661  ]\n", "  [   0.          0.          0.       ..., -697.6708   -697.66846\n", "   -697.6661  ]]]\n", "[DEBUG] Source shape: [ 485 1024], Dest shape: [ 951 1470], Out shape: [25769 39861]\n", "[DEBUG] Warping channel: LB06 BP 4i vimentin green pancytokeratin red 2apr25_R 1_Merged_ch00\n", "[DEBUG] Channel image shape: 27746x13131, bands: 1\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/anaconda3/envs/VALIS2/lib/python3.10/site-packages/valis/valtils.py:25: UserWarning: scaling transformation for image with different shape. However, without knowing all of other image's shapes, the scaling may not be the same for all images, and so may not overlap.\n", "  warnings.warn(warning_msg, warning_type)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[DEBUG] Warped channel shape: 18851x54406, bands: 1\n", "[DEBUG] After bandjoin: 23 bands in warped_slide\n", "[DEBUG] Warping channel: LB06 BP 4i vimentin green pancytokeratin red 2apr25_R 1_Merged_ch01\n", "[DEBUG] Channel image shape: 27746x13131, bands: 1\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/anaconda3/envs/VALIS2/lib/python3.10/site-packages/valis/valtils.py:25: UserWarning: scaling transformation for image with different shape. However, without knowing all of other image's shapes, the scaling may not be the same for all images, and so may not overlap.\n", "  warnings.warn(warning_msg, warning_type)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[DEBUG] Warped channel shape: 18851x54406, bands: 1\n", "[DEBUG] After bandjoin: 24 bands in warped_slide\n", "[DEBUG] Warping channel: LB06 BP 4i vimentin green pancytokeratin red 2apr25_R 1_Merged_ch02\n", "[DEBUG] Channel image shape: 27746x13131, bands: 1\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/anaconda3/envs/VALIS2/lib/python3.10/site-packages/valis/valtils.py:25: UserWarning: scaling transformation for image with different shape. However, without knowing all of other image's shapes, the scaling may not be the same for all images, and so may not overlap.\n", "  warnings.warn(warning_msg, warning_type)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[DEBUG] Warped channel shape: 18851x54406, bands: 1\n", "[DEBUG] After bandjoin: 25 bands in warped_slide\n"]}, {"name": "stderr", "output_type": "stream", "text": ["100%|██████████| 9/9 [00:20<00:00,  2.26s/it]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["saving /mnt/d/Users/<USER>/antho 4i alignment/LB06_Tiffs/aligned_images/slides/merged.ome.tiff (34748 x 69607 and 25 channels)\n", "\n", "[=---------------------------------------------------------------------------------------------------] 1.1% in 49.056 minutes\r"]}, {"name": "stderr", "output_type": "stream", "text": ["IOStream.flush timed out\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[=---------------------------------------------------------------------------------------------------] 1.1% in 49.908 minutes\r"]}], "source": ["\n", " \"\"\"Process images with direct channel handling to ensure proper ordering\"\"\"\n", "print(\"Starting image processing with DIRECT channel handling...\")\n", "# Create output directory\n", "\n", "merged_slide_dir = os.path.join(os.path.split(dst_dir)[0], \"slides\")\n", "pathlib.Path(merged_slide_dir).mkdir(exist_ok=True, parents=True)\n", "\n", "# Get all images\n", "\n", "full_img_list = [os.path.join(src_dir, f) for f in os.listdir(src_dir)]\n", "\n", "# Register rounds using DAPI channels , which all end in \"ch00.tif\"\n", "dapi_imgs = [f for f in full_img_list if f.endswith(\"ch00.tif\")]\n", "print(f\"Found {len(dapi_imgs)} DAPI images for registration\")\n", "\n", "# Initialize VALIS registrar\n", "\n", "registrar = registration.Valis(src_dir, dst_dir, img_list=dapi_imgs, reference_img_f=reference_slide, micro_rigid_registrar_cls=MicroRigidRegistrar,align_to_reference=True)\n", "# Perform registration\n", "print(\"Performing registration...\")\n", "rigid_registrar, non_rigid_registrar, error_df = registrar.register()\n", "\n", "reference_slide = registrar.get_ref_slide()\n", "micro_reg_size = np.floor(np.max(reference_slide.slide_dimensions_wh[0])*micro_reg_fraction).astype(int)\n", "print(f\"Performing micro-registration with size: {micro_reg_size}...\")\n", "micro_reg, micro_error = registrar.register_micro(max_non_rigid_registration_dim_px=micro_reg_size,align_to_reference=True)\n", "\n", "\n", "# Use registration parameters to warp DAPI channel and the other images from the same round\n", "round_dict = {slide_obj:[f for f in full_img_list if get_round_name(f) == get_round_name(slide_obj.src_f)]\n", "              for slide_obj in registrar.slide_dict.values()}\n", "\n", "all_imgs = [os.path.join(src_dir, f) for f in os.listdir(src_dir)]\n", "channel_names = []\n", "warped_slide = None\n", "\n", "for slide_warper, round_slides in tqdm(round_dict.items()):\n", "    print(f\"\\n[DEBUG] Warping images for round: {slide_warper.name}\")\n", "    valtils.sort_nicely(round_slides)\n", "    print(f\"[DEBUG] Files in this round: {round_slides}\")\n", "    # Extract transformation parameters from the DAPI slide_warper\n", "    M = slide_warper.M\n", "    bk_dxdy = slide_warper.bk_dxdy\n", "    transformation_src_shape_rc = slide_warper.processed_img_shape_rc\n", "    transformation_dst_shape_rc = slide_warper.reg_img_shape_rc\n", "    out_shape_rc = slide_warper.aligned_slide_shape_rc\n", "    print(f\"[DEBUG] Transformation M: {M}\")\n", "    print(f\"[DEBUG] Non-rigid bk_dxdy: {bk_dxdy}\")\n", "    print(f\"[DEBUG] Source shape: {transformation_src_shape_rc}, Dest shape: {transformation_dst_shape_rc}, Out shape: {out_shape_rc}\")\n", "\n", "    for src_f in round_slides:\n", "        img_name = valtils.get_name(src_f)\n", "        print(f\"[DEBUG] Warping channel: {img_name}\")\n", "        channel_names.append(img_name)\n", "        # Read the channel image\n", "        channel_img = pyvips.Image.new_from_file(src_f)\n", "        print(f\"[DEBUG] Channel image shape: {channel_img.width}x{channel_img.height}, bands: {channel_img.bands}\")\n", "        # Apply the DAPI transformation to this channel\n", "        warped_channel = slide_warper.warp_img(\n", "            img=channel_img, crop=False\n", "            \n", "        )\n", "        print(f\"[DEBUG] Warped channel shape: {warped_channel.width}x{warped_channel.height}, bands: {warped_channel.bands}\")\n", "        if warped_slide is None:\n", "            warped_slide = warped_channel\n", "            print(f\"[DEBUG] Created initial warped_slide with {warped_slide.bands} bands\")\n", "        else:\n", "            warped_slide = warped_slide.bandjoin(warped_channel)\n", "            print(f\"[DEBUG] After bandjoin: {warped_slide.bands} bands in warped_slide\")\n", "\n", "\n", "merged_slide_f = os.path.join(merged_slide_dir, \"merged.ome.tiff\")\n", "merged_ome_xml = get_ome_xml(warped_slide, reference_slide, channel_names)\n", "slide_io.save_ome_tiff(img=warped_slide, dst_f=merged_slide_f, ome_xml=merged_ome_xml)\n", "\n", "# registrar.draw_matches(registrar.dst_dir) # Uncomment to save images showing matched features\n", "\n", "# # Use registration parameters to warp DAPI channel and the other images from the same round\n", "# # Group images by round\n", "# print(\"Grouping images by round...\")\n", "# round_dict = {}\n", "# for slide_obj in registrar.slide_dict.values():\n", "#     round_name = get_round_name(slide_obj.src_f)\n", "#     round_files = [f for f in full_img_list if get_round_name(f) == round_name]\n", "#     round_dict[round_name] = {\n", "#         'warper': slide_obj,\n", "#         'files': round_files\n", "#     }\n", "\n", "# print(f\"Found {len(round_dict)} rounds to process\")\n", "# all_warped_channels = []\n", "# channel_names = []\n", "# original_filenames = []  # Track original filenames for debugging output\n", "\n", "# # Process each round\n", "# print(\"Processing rounds and channels...\")\n", "# for round_name, round_info in tqdm(round_dict.items()):\n", "#     print(f\"\\nProcessing round: {round_name}\")\n", "#     slide_warper = round_info['warper']\n", "#     round_files = round_info['files']\n", "    \n", "#     # Sort files by channel number to ensure correct order (ch00, ch01, ch02...)\n", "#     round_files.sort(key=get_channel_number)\n", "    \n", "#     # Process each channel file in this round\n", "#     for channel_file in round_files:\n", "#         channel_num = get_channel_number(channel_file)\n", "#         filename = os.path.basename(channel_file)\n", "#         print(f\"  Processing channel {channel_num} from {filename}\")\n", "        \n", "#         # Read the channel image\n", "#         channel_img = pyvips.Image.new_from_file(channel_file)\n", "        \n", "#         # Apply warping transformation from the round's warper\n", "#         warped_channel = slide_warper.warp_slide(\n", "#             channel_img, \n", "#             level,\n", "#             rigid_registrar, \n", "#             non_rigid_registrar, \n", "#             micro_reg)\n", "        \n", "#         # Add to our collection of warped channels\n", "#         all_warped_channels.append(warped_channel)\n", "#         original_filenames.append(filename)  # Store the original filename\n", "        \n", "#         # Create a meaningful channel name\n", "#         channel_name = f\"{round_name}_ch{channel_num:02d}\"\n", "#         channel_names.append(channel_name)\n", "#         print(f\"  Added {channel_name} to channel list\")\n", "\n", "# # Combine all channels into a single multi-band image\n", "# print(\"\\nCombining all channels into a single multi-band image...\")\n", "# if not all_warped_channels:\n", "#     raise ValueError(\"No warped channels were created!\")\n", "\n", "# # Start with the first channel\n", "# warped_slide = all_warped_channels[0]\n", "# print(f\"Starting with first channel: {warped_slide.width}x{warped_slide.height}, bands: {warped_slide.bands}, file: {original_filenames[0]}\")\n", "\n", "# # Add each additional channel\n", "# for i, channel in enumerate(all_warped_channels[1:], 1):\n", "#     print(f\"Adding channel {i}: {channel.width}x{channel.height}, bands: {channel.bands}, file: {original_filenames[i]}\")\n", "#     warped_slide = warped_slide.bandjoin(channel)\n", "#     print(f\" Combined image now has {warped_slide.bands} bands\")\n", "\n", "# # Generate OME-XML and save the merged slide\n", "# print(\"\\nGenerating OME-XML and saving merged slide...\")\n", "# reference_slide = registrar.get_ref_slide()\n", "# merged_ome_xml = get_ome_xml(warped_slide, reference_slide, channel_names)\n", "# merged_slide_f = os.path.join(merged_slide_dir, \"merged_direct.ome.tiff\")\n", "\n", "# print(f\"Saving merged slide with {len(channel_names)} channels to: {merged_slide_f}\")\n", "# print(f\"Final image dimensions: {warped_slide.width}x{warped_slide.height}, bands: {warped_slide.bands}\")\n", "# print(f\"Channel names: {channel_names}\")\n", "\n", "# # Save the merged slide\n", "# slide_io.save_ome_tiff(img=warped_slide, dst_f=merged_slide_f, ome_xml=merged_ome_xml)\n", "# print(f\"Successfully saved merged slide to: {merged_slide_f}\")\n", "\n", "# # Also save individual channels for verification\n", "# print(\"\\nSaving individual channels for verification...\")\n", "# for i, channel in enumerate(all_warped_channels):\n", "#     channel_name = channel_names[i]\n", "#     original_name = original_filenames[i]\n", "#     channel_file = os.path.join(merged_slide_dir, f\"channel_{i}_{channel_name}.tiff\")\n", "#     print(f\"Saving channel {i}: {channel_file} (from {original_name})\")\n", "#     channel.tiffsave(channel_file, compression=\"lzw\")\n", "\n", "# return merged_slide_f"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\n", "print(slide_warper.warp_img)  # See what methods/attributes it has"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["slide_warper.aligned_slide_shape_rc"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import os\n", "import numpy as np\n", "import logging\n", "import gc\n", "from tqdm import tqdm\n", "from concurrent.futures import ThreadPoolExecutor\n", "\n", "# Configure logging\n", "logging.basicConfig(level=logging.INFO)\n", "\n", "# Define main paths\n", "main_dir = \"/mnt/c/Users/<USER>/4icellsneretti/sorted FOV/\"\n", "output_base_dir = \"/mnt/c/Users/<USER>/4icellsneretti/ome-tiff\"\n", "micro_reg_fraction = 0.25  # Fraction for non-rigid registration\n", "\n", "# Create the output directory if it doesn't exist\n", "os.makedirs(output_base_dir, exist_ok=True)\n", "\n", "# Helper functions\n", "def get_available_workers(max_workers=None):\n", "    # Get the total number of CPU cores\n", "    cpu_count = os.cpu_count()\n", "    \n", "    # Adjust this number based on system load or reserve some cores\n", "    if max_workers:\n", "        num_workers = min(cpu_count, max_workers)\n", "    else:\n", "        num_workers = max(1, int(cpu_count * 0.75))  # Ensure at least 1 worker is available\n", "    \n", "    logging.info(f\"Using {num_workers} workers out of {cpu_count} available CPUs.\")\n", "    return num_workers\n", "\n", "def get_reference_slide(subfolder_name):\n", "    ref_slide_path = os.path.join(main_dir, subfolder_name, f\"{subfolder_name.split('sorted')[-1]}_round1_ch00.tiff\")\n", "    logging.debug(f\"Reference slide path for {subfolder_name}: {ref_slide_path}\")\n", "    return ref_slide_path\n", "\n", "def get_ome_xml(warped_slide, reference_slide, channel_names=None):\n", "    ref_meta = reference_slide.reader.metadata\n", "    bf_dtype = slide_io.vips2bf_dtype(warped_slide.format)\n", "    out_xyczt = slide_io.get_shape_xyzct((warped_slide.width, warped_slide.height), warped_slide.bands)\n", "    ome_xml_obj = slide_io.create_ome_xml(\n", "        shape_xyzct=out_xyczt,\n", "        bf_dtype=bf_dtype,\n", "        is_rgb=False,\n", "        pixel_physical_size_xyu=ref_meta.pixel_physical_size_xyu,\n", "        channel_names=channel_names,\n", "        colormap=None\n", "    )\n", "    return ome_xml_obj.to_xml()\n", "\n", "def get_round_name(src_f):\n", "    img_name = os.path.basename(src_f).lower()\n", "    try:\n", "        # Extract the part that starts with 'round'\n", "        round_part = next(part for part in img_name.split('_') if part.startswith('round'))\n", "        \n", "        # Get the correct round name (rXXcXXfXX_roundX)\n", "        round_name = '_'.join(img_name.split('_')[:2])  # Get the first 2 parts (rXXcXXfXX, roundX)\n", "        \n", "        logging.debug(f\"Extracted round name: {round_name}\")\n", "        \n", "    except (IndexError, StopIteration) as e:\n", "        logging.error(f\"Error extracting round name from filename {img_name}: {e}\")\n", "        round_name = 'unknown'\n", "\n", "    return round_name\n", "\n", "def process_subfolder(subfolder_path):\n", "    subfolder_name = os.path.basename(subfolder_path)\n", "    logging.debug(f\"Processing subfolder: {subfolder_name}\")\n", "\n", "    reference_slide = get_reference_slide(subfolder_name)\n", "\n", "    if not os.path.exists(reference_slide):\n", "        logging.error(f\"Reference slide not found: {reference_slide}\")\n", "        return\n", "\n", "    logging.info(f\"Processing subfolder: {subfolder_name} using reference slide: {reference_slide}\")\n", "\n", "    # List all TIFF files in the subfolder\n", "    \n", "    full_img_list = [os.path.join(subfolder_path, f) for f in os.listdir(subfolder_path)]\n", "    logging.info(f\"Full image list: {full_img_list}\")\n", "\n", "    # Register rounds using DAPI channels , which all end in \"ch00.tiff\"\n", "    dapi_imgs = [f for f in full_img_list if f.endswith(\"ch00.tiff\")]\n", "    registrar = registration.Valis(subfolder_path, output_base_dir, img_list=dapi_imgs, reference_img_f=reference_slide, align_to_reference=True)\n", "    rigid_registrar, non_rigid_registrar, error_df = registrar.register()\n", "\n", "    reference_slide_obj = registrar.get_ref_slide()\n", "    micro_reg_size = np.floor(np.max(reference_slide_obj.slide_dimensions_wh[0]) * micro_reg_fraction).astype(int)\n", "    micro_reg, micro_error = registrar.register_micro(max_non_rigid_registration_dim_px=micro_reg_size, align_to_reference=True)\n", "    \n", "    \n", "    # Create a dictionary with the slides grouped by their round (like round1, round2, etc.)\n", "    round_dict = {slide_obj: [f for f in full_img_list if get_round_name(f) == get_round_name(slide_obj.src_f)] \n", "                  for slide_obj in registrar.slide_dict.values()}\n", "\n", "    all_imgs = [os.path.join(subfolder_path, f) for f in os.listdir(subfolder_path)]\n", "    channel_names = []\n", "    warped_slide = None\n", "\n", "    for slide_warper, round_slides in tqdm(round_dict.items()):\n", "        logging.info(f\"Warping images associated with {get_round_name(slide_warper.src_f)}\")\n", "        \n", "        # Sort the images based on both round and channel to ensure proper order\n", "        try:\n", "            valtils.sort_nicely(round_slides)\n", "        except Exception as e:\n", "            logging.error(f\"Error sorting slides: {e}\")\n", "            continue\n", "        \n", "        logging.debug(f\"Sorted round slides: {round_slides}\")\n", "\n", "        for src_f in round_slides:\n", "            img_name = valtils.get_name(src_f)\n", "            channel_names.append(img_name)\n", "            \n", "            logging.info(f\"Processing channel: {img_name}\")\n", "\n", "            try:\n", "                warped_channel = slide_warper.warp_slide(src_f=src_f, level=0)\n", "                logging.debug(f\"Warped channel shape: {warped_channel.shape}\")\n", "            except Exception as e:\n", "                logging.error(f\"Error warping slide {src_f}: {e}\")\n", "                continue\n", "\n", "            if warped_slide is None:\n", "                warped_slide = warped_channel\n", "            else:\n", "                warped_slide = warped_slide.bandjoin(warped_channel)\n", "            logging.info(f\"Processed channel: {img_name}\")\n", "\n", "    if warped_slide is None:\n", "        logging.error(\"No warped slide created.\")\n", "        return\n", "\n", "    reference_slide_obj = registrar.get_ref_slide()\n", "    merged_ome_xml = get_ome_xml(warped_slide, reference_slide_obj, channel_names)\n", "\n", "    ome_tiff_name = f\"merged_{subfolder_name}.ome.tiff\"\n", "    ome_tiff_path = os.path.join(output_base_dir, ome_tiff_name)\n", "\n", "    try:\n", "        slide_io.save_ome_tiff(img=warped_slide, dst_f=ome_tiff_path, ome_xml=merged_ome_xml)\n", "        logging.info(f\"Saved: {ome_tiff_path}\")\n", "    except Exception as e:\n", "        logging.error(f\"Error saving OME-TIFF file: {e}\")\n", "\n", "    del warped_slide\n", "    gc.collect()\n", "    \n", "def process_all_folders():\n", "    subfolders = [os.path.join(main_dir, subfolder) for subfolder in sorted(os.listdir(main_dir))\n", "                  if os.path.isdir(os.path.join(main_dir, subfolder)) and subfolder != 'ome-tiff']\n", "\n", "    if len(subfolders) == 0:\n", "        logging.info(\"No valid subfolders to process.\")\n", "        return\n", "\n", "    logging.debug(f\"Subfolders found: {len(subfolders)}\")\n", "\n", "    num_threads = get_available_workers(max_workers=len(subfolders))\n", "\n", "    batch_size = 1\n", "    for i in range(0, len(subfolders), batch_size):\n", "        batch = subfolders[i:i + batch_size]\n", "\n", "        with ThreadPoolExecutor(max_workers=num_threads) as executor:\n", "            executor.map(process_subfolder, batch)\n", "\n", "        logging.info(f\"Processed batch {i // batch_size + 1}\")\n", "\n", "    logging.info(\"All folders processed.\")\n", "\n", "if __name__ == \"__main__\":\n", "    process_all_folders()\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["#no multithreading : \n", "import os\n", "import numpy as np\n", "import logging\n", "from tqdm import tqdm\n", "import gc\n", "\n", "# Configure logging\n", "logging.basicConfig(level=logging.DEBUG)\n", "\n", "# Define main paths\n", "main_dir = \"/mnt/d/sorted FOV/\"\n", "output_base_dir = \"/mnt/c/Users/<USER>/4icellsneretti/\"\n", "micro_reg_fraction = 0.25  # Fraction for non-rigid registration\n", "\n", "# Create the output directory if it doesn't exist\n", "os.makedirs(output_base_dir, exist_ok=True)\n", "\n", "# Function to extract the reference slide dynamically for each subfolder\n", "def get_reference_slide(subfolder_name):\n", "    ref_slide_path = os.path.join(main_dir, subfolder_name, f\"{subfolder_name.split('sorted')[-1]}_round1_ch00.tiff\")\n", "    logging.debug(f\"Reference slide path for {subfolder_name}: {ref_slide_path}\")\n", "    return ref_slide_path\n", "\n", "# Function to generate OME-XML metadata for the warped image\n", "def get_ome_xml(warped_slide, reference_slide, channel_names=None):\n", "    ref_meta = reference_slide.reader.metadata\n", "    bf_dtype = slide_io.vips2bf_dtype(warped_slide.format)\n", "    out_xyczt = slide_io.get_shape_xyzct((warped_slide.width, warped_slide.height), warped_slide.bands)\n", "    ome_xml_obj = slide_io.create_ome_xml(\n", "        shape_xyzct=out_xyczt,\n", "        bf_dtype=bf_dtype,\n", "        is_rgb=False,\n", "        pixel_physical_size_xyu=ref_meta.pixel_physical_size_xyu,\n", "        channel_names=channel_names,\n", "        colormap=None\n", "    )\n", "    return ome_xml_obj.to_xml()\n", "\n", "# Extract the round name from the filename\n", "def get_round_name(src_f):\n", "    # Example filename: r02c02f01_round1_ch00.tiff\n", "    img_name = os.path.basename(src_f)\n", "    round_part = img_name.split('_round')[1]\n", "    round_name = round_part.split('_ch')[0]\n", "    return round_name\n", "\n", "# Function to process a single subfolder\n", "def process_subfolder(subfolder_path):\n", "    subfolder_name = os.path.basename(subfolder_path)\n", "    logging.debug(f\"Processing subfolder: {subfolder_name}\")\n", "\n", "    reference_slide = get_reference_slide(subfolder_name)\n", "\n", "    if not os.path.exists(reference_slide):\n", "        logging.error(f\"Reference slide not found: {reference_slide}\")\n", "        return\n", "\n", "    logging.info(f\"Processing subfolder: {subfolder_name} using reference slide: {reference_slide}\")\n", "\n", "    # List all .tiff files in the subfolder\n", "    full_img_list = [os.path.join(subfolder_path, f) for f in os.listdir(subfolder_path) if f.endswith(\".tiff\")]\n", "\n", "    # Perform registration using DAPI channels (assumed to be \"ch00.tiff\")\n", "    dapi_imgs = [f for f in full_img_list if f.endswith(\"ch00.tiff\")]\n", "    registrar = registration.Valis(subfolder_path, output_base_dir, img_list=dapi_imgs, reference_img_f=reference_slide, align_to_reference=True)\n", "    rigid_registrar, non_rigid_registrar, error_df = registrar.register()\n", "\n", "    reference_slide_obj = registrar.get_ref_slide()\n", "    micro_reg_size = np.floor(np.max(reference_slide_obj.slide_dimensions_wh[0]) * micro_reg_fraction).astype(int)\n", "    micro_reg, micro_error = registrar.register_micro(max_non_rigid_registration_dim_px=micro_reg_size, align_to_reference=True)\n", "\n", "    # Warp images based on registration results\n", "    round_dict = {slide_obj: [f for f in full_img_list if get_round_name(f) == get_round_name(slide_obj.src_f)] for slide_obj in registrar.slide_dict.values()}\n", "    \n", "    channel_names = []\n", "    warped_slide = None\n", "\n", "    for slide_warper, round_slides in tqdm(round_dict.items()):\n", "        logging.info(f\"Warping images associated with {slide_warper.name}\")\n", "        valtils.sort_nicely(round_slides)\n", "        \n", "        for src_f in round_slides:\n", "            img_name = valtils.get_name(src_f)\n", "            channel_names.append(img_name)\n", "            warped_channel = slide_warper.warp_slide(src_f=src_f, level=0)\n", "            \n", "            if warped_slide is None:\n", "                warped_slide = warped_channel\n", "            else:\n", "                warped_slide = warped_slide.bandjoin(warped_channel)\n", "\n", "    # Create OME-XML metadata\n", "    merged_ome_xml = get_ome_xml(warped_slide, reference_slide_obj, channel_names)\n", "\n", "    ome_tiff_name = f\"sorted_{subfolder_name}.ome.tiff\"\n", "    ome_tiff_path = os.path.join(output_base_dir, ome_tiff_name)\n", "    \n", "    try:\n", "        slide_io.save_ome_tiff(img=warped_slide, dst_f=ome_tiff_path, ome_xml=merged_ome_xml)\n", "        logging.info(f\"Saved: {ome_tiff_path}\")\n", "    except Exception as e:\n", "        logging.error(f\"Error saving {ome_tiff_path}: {e}\")\n", "\n", "    # Clear memory\n", "    del warped_slide\n", "    gc.collect()\n", "\n", "# Function to process all subfolders sequentially\n", "def process_all_folders():\n", "    subfolders = [os.path.join(main_dir, subfolder) for subfolder in sorted(os.listdir(main_dir))\n", "                  if os.path.isdir(os.path.join(main_dir, subfolder)) and subfolder != 'ome-tiff']\n", "\n", "    if len(subfolders) == 0:\n", "        logging.info(\"No valid subfolders to process.\")\n", "        return\n", "\n", "    logging.info(f\"Found {len(subfolders)} subfolders to process.\")\n", "\n", "    # Process each subfolder sequentially\n", "    for subfolder in subfolders:\n", "        process_subfolder(subfolder)\n", "        logging.info(f\"Completed processing subfolder: {os.path.basename(subfolder)}\")\n", "\n", "    logging.info(\"All folders processed.\")\n", "\n", "# Main entry point\n", "if __name__ == \"__main__\":\n", "    process_all_folders()\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["src_dir = \"/mnt/c/Users/<USER>/antho 4i alignment/imageyoung207ua\"\n", "dst_dir = \"/mnt/c/Users/<USER>/antho 4i alignment/results young207ua FILLA/\"\n", "reference_slide = \"/mnt/c/Users/<USER>/antho 4i alignment/imageyoung207ua/young ctrl skin 4i p16 red p21 green 25sep23_Region 4_Merged_ch00.tif\"\n", "micro_reg_fraction = 0.25 # Fraction full resolution used for non-rigid registration"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def get_round_name(src_f):\n", "    img_name = valtils.get_name(src_f)\n", "    round_name = img_name.lower().split(\"_merged\")[0]\n", "    return round_name\n", "\n", "def get_ome_xml(warped_slide, reference_slide, channel_names=None):\n", "  \"\"\"Generate ome-xml for warped slide\n", "\n", "  Parameters\n", "  ----------\n", "  warped_slide : pyvips.Image\n", "    Registered slide that will be saved as an ome.tiff\n", "\n", "  reference_slide : registration.Slide\n", "    Slide object that the others were aligned to/towards\n", "\n", "  channel_names : str, list, optional\n", "    channel names for warped slide. If `None`, then the\n", "    channel names from `src_f` will be used\n", "\n", "\n", "  Returns\n", "  -------\n", "  ome_xml : str\n", "    String of the ome-xml metadata\n", "\n", "  \"\"\"\n", "\n", "  ref_meta = reference_slide.reader.metadata\n", "\n", "  bf_dtype = slide_io.vips2bf_dtype(warped_slide.format)\n", "  out_xyczt = slide_io.get_shape_xyzct((warped_slide.width, warped_slide.height), warped_slide.bands)\n", "  ome_xml_obj = slide_io.create_ome_xml(shape_xyzct=out_xyczt,\n", "                                        bf_dtype=bf_dtype,\n", "                                        is_rgb=False,\n", "                                        pixel_physical_size_xyu=ref_meta.pixel_physical_size_xyu,\n", "                                        channel_names=channel_names,\n", "                                        colormap=None)\n", "\n", "  ome_xml = ome_xml_obj.to_xml()\n", "\n", "  return ome_xml"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["merged_slide_dir = os.path.join(os.path.split(dst_dir)[0], \"slides\")\n", "pathlib.Path(merged_slide_dir).mkdir(exist_ok=True, parents=True)\n", "\n", "full_img_list = [os.path.join(src_dir, f) for f in os.listdir(src_dir)]\n", "\n", "# Register rounds using DAPI channels , which all end in \"ch00.tif\"\n", "dapi_imgs = [f for f in full_img_list if f.endswith(\"ch00.tif\")]\n", "\n", "registrar = registration.Valis(src_dir, dst_dir, img_list=dapi_imgs, reference_img_f=reference_slide, micro_rigid_registrar_cls=None,align_to_reference=True)\n", "rigid_registrar, non_rigid_registrar, error_df = registrar.register()\n", "\n", "reference_slide = registrar.get_ref_slide()\n", "micro_reg_size = np.floor(np.max(reference_slide.slide_dimensions_wh[0])*micro_reg_fraction).astype(int)\n", "micro_reg, micro_error = registrar.register_micro(max_non_rigid_registration_dim_px=micro_reg_size,align_to_reference=True)\n", "\n", "\n", "# registrar.draw_matches(registrar.dst_dir) # Uncomment to save images showing matched features\n", "\n", "# Use registration parameters to warp DAPI channel and the other images from the same round\n", "round_dict = {slide_obj:[f for f in full_img_list if get_round_name(f) == get_round_name(slide_obj.src_f)]\n", "              for slide_obj in registrar.slide_dict.values()}\n", "\n", "all_imgs = [os.path.join(src_dir, f) for f in os.listdir(src_dir)]\n", "channel_names = []\n", "warped_slide = None\n", "\n", "for slide_warper, round_slides in tqdm(round_dict.items()):\n", "    print(f\"warping images associated with {slide_warper.name}\")\n", "    valtils.sort_nicely(round_slides)\n", "    for src_f in round_slides:\n", "        img_name = valtils.get_name(src_f)\n", "        channel_names.append(img_name)\n", "        warped_channel = slide_warper.warp_slide(src_f=src_f, level=0)\n", "        if warped_slide is None:\n", "            warped_slide = warped_channel\n", "        else:\n", "            warped_slide = warped_slide.bandjoin(warped_channel)\n", "\n", "reference_slide = registrar.get_ref_slide()\n", "merged_ome_xml = get_ome_xml(warped_slide, reference_slide, channel_names)\n", "merged_slide_f = os.path.join(merged_slide_dir, \"merged.ome.tiff\")\n", "\n", "slide_io.save_ome_tiff(img=warped_slide, dst_f=merged_slide_f, ome_xml=merged_ome_xml)"]}], "metadata": {"kernelspec": {"display_name": "VALIS2", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.13"}}, "nbformat": 4, "nbformat_minor": 2}